import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:wargani/app/app.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/models/user_model.dart';

void main() {
  group('Footer Fix Test', () {
    testWidgets('Test receipt generation with footer visibility', (WidgetTester tester) async {
      // Initialize Hive
      await HiveHelper.init();
      
      // Create test profile
      final profile = Profile(
        mandalName: 'श्री गणेश मंडळ',
        address: 'पुणे, महाराष्ट्र',
        currentYear: '2024',
        mandalRegistrationNo: 'MH/2024/001',
      );
      
      final profileBox = HiveHelper.getProfileBox();
      await profileBox.clear();
      await profileBox.add(profile);
      
      // Create test user
      final usersBox = HiveHelper.getUsersBox();
      await usersBox.clear();
      await usersBox.add(User(
        uid: 'test-uid',
        name: 'Test User',
        email: '<EMAIL>',
        password: 'test123',
        secretQuestion: 'Test Question',
        secretAnswer: 'Test Answer',
      ));
      
      // Build the app
      await tester.pumpWidget(const WarganiApp());
      await tester.pumpAndSettle();
      
      // Create test wargani data
      final wargani = Wargani(
        receiptNo: 1,
        name: 'श्री राम पाटील',
        amount: 2345.0,
        date: DateTime.now(),
        prefix: 'सो.श्री',
        mobileNo: '9876543210',
        amountInWords: 'दोन हजार तीनशे पंचेचाळीस रुपये',
        registrationNo: 'REG001',
      );
      
      // Test PDF generation
      final context = tester.element(find.byType(Scaffold));
      
      try {
        print('🔄 Testing old format receipt generation (with footer fix)...');
        final pdfPath = await PdfGenerator.generate(
          context,
          wargani,
          'Test User',
        );
        
        // Verify PDF was generated
        expect(pdfPath, isNotNull);
        print('✅ Old format receipt PDF generated successfully: $pdfPath');
        print('📄 Footer should now be visible in the generated receipt');
        
        // Test new professional format as well
        print('🔄 Testing professional format receipt generation...');
        final professionalPdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
          context,
          wargani,
          'Test User',
        );
        
        expect(professionalPdfPath, isNotNull);
        print('✅ Professional format receipt PDF generated successfully: $professionalPdfPath');
        
      } catch (e) {
        print('❌ Error generating receipt PDF: $e');
        fail('PDF generation failed: $e');
      }
    });
  });
}
