// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wargani_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class WarganiAdapter extends TypeAdapter<Wargani> {
  @override
  final int typeId = 1;

  @override
  Wargani read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Wargani(
      receiptNo: fields[0] as int,
      name: fields[1] as String,
      amount: fields[2] as double,
      date: fields[3] as DateTime,
      prefix: fields[4] as String,
      mobileNo: fields[5] as String?,
      registrationNo: fields[6] as String?,
      amountInWords: fields[7] as String?,
      paymentMethod: fields[8] as String?,
      deleteReason: fields[9] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Wargani obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.receiptNo)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.amount)
      ..writeByte(3)
      ..write(obj.date)
      ..writeByte(4)
      ..write(obj.prefix)
      ..writeByte(5)
      ..write(obj.mobileNo)
      ..writeByte(6)
      ..write(obj.registrationNo)
      ..writeByte(7)
      ..write(obj.amountInWords)
      ..writeByte(8)
      ..write(obj.paymentMethod)
      ..writeByte(9)
      ..write(obj.deleteReason);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WarganiAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
