import 'package:wargani/features/authentication/domain/entities/user_entity.dart';
import 'package:wargani/models/user_model.dart';
import 'package:wargani/utils/hive_helper.dart';

/// Local data source for authentication operations
class AuthLocalDataSource {
  
  /// <PERSON>gin user with email and password
  Future<UserEntity?> login(String email, String password) async {
    try {
      final usersBox = HiveHelper.getUsersBox();
      
      // Check if any user exists with the provided email and password
      for (int i = 0; i < usersBox.length; i++) {
        final user = usersBox.getAt(i);
        if (user != null && user.email == email && user.password == password) {
          return UserEntity(
            uid: user.uid,
            name: user.name,
            email: user.email,
            password: user.password,
            secretQuestion: user.secretQuestion,
            secretAnswer: user.secretAnswer,
            createdAt: DateTime.now(), // Since User model doesn't have createdAt, use current time
          );
        }
      }
      
      return null;
    } catch (e) {
      throw Exception('Failed to login: $e');
    }
  }
  
  /// Register new user
  Future<UserEntity> register(String name, String email, String password, String secretQuestion, String secretAnswer) async {
    try {
      final usersBox = HiveHelper.getUsersBox();
      
      // Check if user already exists
      for (int i = 0; i < usersBox.length; i++) {
        final user = usersBox.getAt(i);
        if (user != null && user.email == email) {
          throw Exception('User with this email already exists');
        }
      }
      
      // Create new user
      final newUser = User(
        uid: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        email: email,
        password: password,
        secretQuestion: secretQuestion,
        secretAnswer: secretAnswer,
      );

      // Save to Hive
      await HiveHelper.getUsersBox().add(newUser);

      // Return as UserEntity
      return UserEntity(
        uid: newUser.uid!,
        name: newUser.name,
        email: newUser.email,
        password: newUser.password,
        secretQuestion: newUser.secretQuestion,
        secretAnswer: newUser.secretAnswer,
        createdAt: DateTime.now(),
      );
    } catch (e) {
      throw Exception('Failed to register: $e');
    }
  }
  
  /// Logout user
  Future<void> logout() async {
    try {
      // Clear any stored authentication tokens or session data
      // For now, this is a no-op since we're using simple local storage
    } catch (e) {
      throw Exception('Failed to logout: $e');
    }
  }
  
  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    try {
      final usersBox = HiveHelper.getUsersBox();
      return usersBox.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
  
  /// Get current user
  Future<UserEntity?> getCurrentUser() async {
    try {
      final usersBox = HiveHelper.getUsersBox();
      if (usersBox.isNotEmpty) {
        final user = usersBox.getAt(0);
        if (user != null) {
          return UserEntity(
            uid: user.uid,
            name: user.name,
            email: user.email,
            password: user.password,
            secretQuestion: user.secretQuestion,
            secretAnswer: user.secretAnswer,
            createdAt: DateTime.now(), // Since User model doesn't have createdAt, use current time
          );
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
