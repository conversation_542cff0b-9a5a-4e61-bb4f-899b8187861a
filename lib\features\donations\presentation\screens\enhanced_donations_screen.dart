import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:share_plus/share_plus.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/core/utils/validators.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/donation_model.dart';
import 'package:wargani/shared/widgets/custom_button.dart';
import 'package:wargani/shared/widgets/custom_card.dart';
import 'package:wargani/shared/widgets/custom_text_field.dart';
import 'package:wargani/shared/widgets/loading_overlay.dart';
import 'package:wargani/features/donations/presentation/screens/deleted_donations_screen.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// Enhanced Donations Screen with professional UI
class EnhancedDonationsScreen extends StatefulWidget {
  const EnhancedDonationsScreen({super.key});

  @override
  State<EnhancedDonationsScreen> createState() => _EnhancedDonationsScreenState();
}

class _EnhancedDonationsScreenState extends State<EnhancedDonationsScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  
  // Controllers
  final _donorNameController = TextEditingController();
  final _amountController = TextEditingController();
  final _reasonController = TextEditingController();

  // Focus Nodes
  final _donorNameFocus = FocusNode();
  final _amountFocus = FocusNode();
  final _reasonFocus = FocusNode();

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  late AnimationController _animationController;
  late AnimationController _fabAnimationController;

  // Tab controller - same as Wargani
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _tabController = TabController(length: 2, vsync: this);
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );
    _fabAnimationController = AnimationController(
      duration: AppConstants.shortAnimation,
      vsync: this,
    );
    _animationController.forward();
    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fabAnimationController.dispose();
    _tabController.dispose();
    _donorNameController.dispose();
    _amountController.dispose();
    _reasonController.dispose();
    _donorNameFocus.dispose();
    _amountFocus.dispose();
    _reasonFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return LoadingOverlay(
      isLoading: _isLoading,
      message: 'Processing donation...',
      child: Scaffold(
        backgroundColor: context.colorScheme.background,
        appBar: _buildAppBar(localizations),
        body: Column(
          children: [
            _buildTabBar(localizations),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildDonationForm(localizations),
                  _buildDonationsList(localizations),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(AppLocalizations localizations) {
    return AppBar(
      title: Text(localizations.donations),
      backgroundColor: context.colorScheme.surface,
      elevation: 0,
    );
  }

  Widget _buildTabBar(AppLocalizations localizations) {
    return Container(
      color: context.colorScheme.surface,
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(
            icon: Icon(Icons.add_rounded),
            text: 'New Donation',
          ),
          Tab(
            icon: Icon(Icons.list_rounded),
            text: 'All Donations',
          ),
        ],
        labelColor: context.colorScheme.primary,
        unselectedLabelColor: context.colorScheme.onSurface.withValues(alpha: 0.6),
        indicatorColor: context.colorScheme.primary,
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildDonationForm(AppLocalizations localizations) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFormHeader(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildBasicInfoSection(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildContactInfoSection(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildAdditionalInfoSection(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildActionButtons(localizations),
            const SizedBox(height: AppConstants.largePadding),
          ],
        ),
      ),
    ).animate().fadeIn().slideY(begin: 0.3, end: 0);
  }

  Widget _buildBasicInfoSection(AppLocalizations localizations) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: context.colorScheme.primary,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.donorName,
            hint: 'Enter donor name',
            controller: _donorNameController,
            focusNode: _donorNameFocus,
            prefixIcon: Icons.person_rounded,
            validator: Validators.validateName,
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _amountFocus.requestFocus(),
            isDense: true,
            enableTranslation: true,
          ),
          const SizedBox(height: 12),
          CustomTextField(
            label: localizations.amount,
            hint: 'Amount in ₹',
            controller: _amountController,
            focusNode: _amountFocus,
            keyboardType: TextInputType.number,
            prefixIcon: Icons.currency_rupee_rounded,
            validator: Validators.validateAmount,
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _reasonFocus.requestFocus(),
            isDense: true,
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfoSection(AppLocalizations localizations) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Additional Information',
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: context.colorScheme.primary,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.reason,
            hint: 'Reason (optional)',
            controller: _reasonController,
            focusNode: _reasonFocus,
            prefixIcon: Icons.note_rounded,
            isDense: true,
            enableTranslation: true,
            maxLines: 2,
            textInputAction: TextInputAction.done,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoSection(AppLocalizations localizations) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Date Selection',
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: context.colorScheme.primary,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildDateSelector(localizations),
        ],
      ),
    );
  }

  Widget _buildActionButtons(AppLocalizations localizations) {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: localizations.cancel,
            variant: ButtonVariant.secondary,
            onPressed: () {
              _clearForm();
              _tabController.animateTo(1); // Switch to list tab
            },
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: CustomButton(
            text: localizations.save,
            onPressed: _saveDonation,
            isLoading: _isLoading,
            icon: Icons.save_rounded,
          ),
        ),
      ],
    );
  }

  Widget _buildFormHeader(AppLocalizations localizations) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.volunteer_activism_rounded,
            color: Colors.green,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add New Donation',
                style: context.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Record donations received',
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDateSelector(AppLocalizations localizations) {
    return CustomCard(
      backgroundColor: context.colorScheme.surfaceVariant,
      child: Row(
        children: [
          Icon(
            Icons.calendar_today_rounded,
            color: context.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Text(
            '${localizations.date}: ${_selectedDate.toDisplayDate}',
            style: context.textTheme.bodyLarge,
          ),
          const Spacer(),
          CustomButton(
            text: 'Change',
            variant: ButtonVariant.text,
            size: ButtonSize.small,
            onPressed: _selectDate,
          ),
        ],
      ),
    );
  }

  Widget _buildDonationsList(AppLocalizations localizations) {
    return ValueListenableBuilder(
      valueListenable: HiveHelper.getDonationsBox().listenable(),
      builder: (context, Box<Donation> box, _) {
        final donations = box.values.toList().cast<Donation>();
        
        if (donations.isEmpty) {
          return _buildEmptyState(localizations);
        }

        return Column(
          children: [
            // Header with donors report button - same as Wargani
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'All Donations (${donations.length})',
                      style: context.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      CustomButton(
                        text: 'Download Report',
                        variant: ButtonVariant.secondary,
                        size: ButtonSize.small,
                        icon: Icons.download_rounded,
                        onPressed: () => _downloadDonorsReport(),
                      ),
                      const SizedBox(width: 8),
                      CustomButton(
                        text: 'Share Report',
                        variant: ButtonVariant.secondary,
                        size: ButtonSize.small,
                        icon: Icons.share_rounded,
                        onPressed: () => _shareDonorsReport(),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                itemCount: donations.length,
                itemBuilder: (context, index) {
                  final donation = donations[index];
                  return _buildDonationCard(donation, index);
                },
              ),
            ),
          ],
        );
      },
    );
  }



  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.volunteer_activism_rounded,
            size: 64,
            color: context.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            localizations.noDonationsYet,
            style: context.textTheme.headlineSmall?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Record donations received by your mandal',
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
          const SizedBox(height: 24),
          CustomButton(
            text: 'Add First Donation',
            onPressed: () {
              _tabController.animateTo(0); // Switch to form tab
            },
            icon: Icons.volunteer_activism_rounded,
          ),
        ],
      ),
    );
  }

  Widget _buildDonationCard(Donation donation, int index) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '#${donation.key}',
                  style: context.textTheme.labelLarge?.copyWith(
                    color: context.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                donation.date.toDisplayDate,
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.volunteer_activism_rounded,
                size: 20,
                color: context.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  donation.donorName,
                  style: context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                donation.amount.toCurrencyCompact,
                style: context.textTheme.titleLarge?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (donation.reason != null && donation.reason!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.note_rounded,
                    size: 16,
                    color: context.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      donation.reason!,
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: context.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Download',
                  variant: ButtonVariant.secondary,
                  size: ButtonSize.small,
                  icon: Icons.download_rounded,
                  onPressed: () => _downloadDonationPdf(donation),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: CustomButton(
                  text: 'WhatsApp Share',
                  size: ButtonSize.small,
                  icon: Icons.share_rounded,
                  onPressed: () => _shareDonationPdf(donation),
                ),
              ),
              const SizedBox(width: 8),
              CustomButton(
                text: '',
                variant: ButtonVariant.secondary,
                size: ButtonSize.small,
                icon: Icons.delete_rounded,
                onPressed: () => _deleteDonation(donation),
              ),
            ],
          ),
        ],
      ),
    ).animate(delay: (index * 100).ms).fadeIn().slideX(begin: 0.3, end: 0);
  }



  // Helper Methods
  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveDonation() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final donation = Donation(
          donorName: _donorNameController.text.trim(),
          amount: double.parse(_amountController.text),
          reason: _reasonController.text.trim().isNotEmpty
              ? _reasonController.text.trim()
              : null,
          date: _selectedDate,
        );

        await HiveHelper.getDonationsBox().add(donation);
        _clearForm();

        if (mounted) {
          _clearForm();
          _tabController.animateTo(1); // Switch to list tab
          context.showSuccessSnackBar('Donation recorded successfully!');
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar('Failed to record donation: ${e.toString()}');
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _clearForm() {
    _donorNameController.clear();
    _amountController.clear();
    _reasonController.clear();
    _selectedDate = DateTime.now();
  }



  Future<void> _downloadDonorsReport() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final user = HiveHelper.getUsersBox().isNotEmpty
          ? HiveHelper.getUsersBox().getAt(0)
          : null;

      final pdfPath = await PdfGenerator.generateDonorsReport(context);

      if (mounted) {
        context.showSuccessSnackBar('Donors report downloaded successfully!');
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to generate report: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _shareDonorsReport() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final user = HiveHelper.getUsersBox().isNotEmpty
          ? HiveHelper.getUsersBox().getAt(0)
          : null;

      final pdfPath = await PdfGenerator.generateDonorsReport(context);

      if (pdfPath != null) {
        await Share.shareXFiles([XFile(pdfPath)], text: 'Donors Report');
        if (mounted) {
          context.showSuccessSnackBar('Donors report shared successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to share report: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteDonation(Donation donation) async {
    // Show delete reason dialog - same as Wargani with default reasons
    final deleteReason = await showDialog<String>(
      context: context,
      builder: (context) {
        final reasonController = TextEditingController();
        String? selectedReason;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Delete Donation'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Are you sure you want to delete donation from ${donation.donorName}?'),
                  const SizedBox(height: 16),

                  // Default reasons - same as Wargani
                  const Text('Select reason:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),

                  Wrap(
                    spacing: 8,
                    children: [
                      'Duplicate Entry',
                      'Wrong Amount',
                      'Wrong Name',
                      'Cancelled',
                      'Data Error',
                    ].map((reason) => FilterChip(
                      label: Text(reason),
                      selected: selectedReason == reason,
                      onSelected: (selected) {
                        setState(() {
                          selectedReason = selected ? reason : null;
                          if (selected) {
                            reasonController.text = reason;
                          }
                        });
                      },
                    )).toList(),
                  ),

                  const SizedBox(height: 16),
                  TextField(
                    controller: reasonController,
                    decoration: const InputDecoration(
                      labelText: 'Reason for deletion',
                      hintText: 'Enter or select reason...',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                    onChanged: (value) {
                      setState(() {
                        selectedReason = null; // Clear chip selection when typing
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(reasonController.text),
                  child: const Text('Delete'),
                ),
              ],
            );
          },
        );
      },
    );

    if (deleteReason != null && deleteReason.isNotEmpty) {
      try {
        // Move to deleted donations box - same as Wargani
        final deletedDonationsBox = HiveHelper.getDeletedDonationsBox();

        // Create deleted donation entry
        final deletedDonation = {
          'originalKey': donation.key,
          'donorName': donation.donorName,
          'amount': donation.amount,
          'date': donation.date,
          'reason': donation.reason,
          'deleteReason': deleteReason,
          'deletedAt': DateTime.now(),
        };

        await deletedDonationsBox.add(deletedDonation);

        // Remove from main donations box
        await HiveHelper.getDonationsBox().delete(donation.key);

        if (mounted) {
          context.showSuccessSnackBar('Donation moved to bin successfully!');
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar('Failed to delete donation: ${e.toString()}');
        }
      }
    }
  }

  Future<void> _downloadDonationPdf(Donation donation) async {
    try {
      // Show language selection dialog
      final selectedLanguage = await _showLanguageSelectionDialog();

      // If user cancelled the dialog, return gracefully
      if (selectedLanguage == null) {
        return; // User cancelled, no need to show any message
      }

      if (!mounted) return;

      setState(() {
        _isLoading = true;
      });

      final user = HiveHelper.getUsersBox().isNotEmpty
          ? HiveHelper.getUsersBox().getAt(0)
          : null;

      final pdfPath = await PdfGenerator.generateDonationReceipt(
        context,
        donation,
        user?.name,
        forceLanguage: selectedLanguage,
      );

      if (pdfPath != null && mounted) {
        await Share.shareXFiles([XFile(pdfPath)]);
        if (mounted) {
          context.showSuccessSnackBar('Donation receipt downloaded successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to generate PDF: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _shareDonationPdf(Donation donation) async {
    try {
      // Show language selection dialog
      final selectedLanguage = await _showLanguageSelectionDialog();

      // If user cancelled the dialog, return gracefully
      if (selectedLanguage == null) {
        return; // User cancelled, no need to show any message
      }

      if (!mounted) return;

      setState(() {
        _isLoading = true;
      });

      final user = HiveHelper.getUsersBox().isNotEmpty
          ? HiveHelper.getUsersBox().getAt(0)
          : null;

      final pdfPath = await PdfGenerator.generateDonationReceipt(
        context,
        donation,
        user?.name,
        forceLanguage: selectedLanguage,
      );

      if (pdfPath != null && mounted) {
        await Share.shareXFiles(
          [XFile(pdfPath)],
          text: 'Thank you ${donation.donorName} for your generous donation of ₹${donation.amount}!',
        );
        if (mounted) {
          context.showSuccessSnackBar('Donation receipt shared successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to share PDF: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<String?> _showLanguageSelectionDialog() async {
    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Language'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Text('🇮🇳'),
                title: const Text('मराठी (Marathi)'),
                onTap: () => Navigator.of(context).pop('mr'),
              ),
              ListTile(
                leading: const Text('🇮🇳'),
                title: const Text('हिंदी (Hindi)'),
                onTap: () => Navigator.of(context).pop('hi'),
              ),
              ListTile(
                leading: const Text('🇬🇧'),
                title: const Text('English'),
                onTap: () => Navigator.of(context).pop('en'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  void _showDeletedDonations() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DeletedDonationsScreen(),
      ),
    );
  }
}
