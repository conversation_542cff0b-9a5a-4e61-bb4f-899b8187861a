import 'package:flutter/material.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/shared/widgets/custom_card.dart';
import 'package:wargani/shared/widgets/custom_button.dart';
import 'package:wargani/models/donation_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:intl/intl.dart';

class DeletedDonationsScreen extends StatefulWidget {
  const DeletedDonationsScreen({super.key});

  @override
  State<DeletedDonationsScreen> createState() => _DeletedDonationsScreenState();
}

class _DeletedDonationsScreenState extends State<DeletedDonationsScreen> {

  Future<List<Map<dynamic, dynamic>>> _getDeletedDonations() async {
    try {
      final box = HiveHelper.getDeletedDonationsBox();
      return box.values.toList().cast<Map<dynamic, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Deleted Donations'),
        backgroundColor: Colors.red.shade50,
        foregroundColor: Colors.red.shade700,
      ),
      body: FutureBuilder(
        future: _getDeletedDonations(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          final deletedDonations = snapshot.data ?? [];

          if (deletedDonations.isEmpty) {
            return _buildEmptyState();
          }

          return ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: deletedDonations.length,
            itemBuilder: (context, index) {
              final deletedDonation = deletedDonations[index];
              return _buildDeletedDonationCard(deletedDonation, index);
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.delete_outline_rounded,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No Deleted Donations',
            style: context.textTheme.titleLarge?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Deleted donations will appear here',
            style: context.textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeletedDonationCard(Map<dynamic, dynamic> deletedDonation, int index) {
    final donorName = deletedDonation['donorName'] ?? 'Unknown';
    final amount = deletedDonation['amount'] ?? 0.0;
    final date = deletedDonation['date'] ?? DateTime.now();
    final deleteReason = deletedDonation['deleteReason'] ?? 'No reason provided';
    final deletedAt = deletedDonation['deletedAt'] ?? DateTime.now();

    return CustomCard(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      backgroundColor: Colors.red.withValues(alpha: 0.05),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '#${deletedDonation['originalKey'] ?? index + 1}',
                  style: context.textTheme.labelLarge?.copyWith(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                '${(date as DateTime).day}/${(date as DateTime).month}/${(date as DateTime).year}',
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.person_rounded,
                size: 20,
                color: Colors.red.shade700,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  donorName,
                  style: context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                '₹${amount.toStringAsFixed(0)}',
                style: context.textTheme.titleLarge?.copyWith(
                  color: Colors.red.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Delete Reason:',
                  style: context.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  deleteReason,
                  style: context.textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'Deleted on: ${(deletedAt as DateTime).day}/${(deletedAt as DateTime).month}/${(deletedAt as DateTime).year}',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Restore',
                  variant: ButtonVariant.secondary,
                  size: ButtonSize.small,
                  icon: Icons.restore_rounded,
                  onPressed: () => _restoreDonation(deletedDonation, index),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: CustomButton(
                  text: 'Delete Permanently',
                  variant: ButtonVariant.secondary,
                  size: ButtonSize.small,
                  icon: Icons.delete_forever_rounded,
                  onPressed: () => _deletePermanently(index),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _restoreDonation(Map<dynamic, dynamic> deletedDonation, int index) async {
    try {
      // Restore to main donations box
      final donationsBox = HiveHelper.getDonationsBox();

      // Create new donation entry
      final restoredDonation = Donation(
        donorName: deletedDonation['donorName'] ?? '',
        amount: (deletedDonation['amount'] ?? 0.0).toDouble(),
        date: deletedDonation['date'] ?? DateTime.now(),
        reason: deletedDonation['reason'],
      );

      await donationsBox.add(restoredDonation);
      
      // Remove from deleted donations
      await HiveHelper.getDeletedDonationsBox().deleteAt(index);
      
      if (mounted) {
        context.showSuccessSnackBar('Donation restored successfully!');
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to restore donation: ${e.toString()}');
      }
    }
  }

  Future<void> _deletePermanently(int index) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Permanently'),
        content: const Text('This action cannot be undone. Are you sure?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await HiveHelper.getDeletedDonationsBox().deleteAt(index);
        if (mounted) {
          context.showSuccessSnackBar('Donation deleted permanently!');
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar('Failed to delete permanently: ${e.toString()}');
        }
      }
    }
  }
}
