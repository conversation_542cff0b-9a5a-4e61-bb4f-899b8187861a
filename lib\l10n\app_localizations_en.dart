// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Wargani';

  @override
  String get developedBy => 'Developed by AMSSoftX | https://amssoftx.com';

  @override
  String get profile => 'Profile';

  @override
  String get mandalName => 'Mandal Name';

  @override
  String get address => 'Address';

  @override
  String get logo => 'Logo';

  @override
  String get currentYear => 'Current Year';

  @override
  String get save => 'Save';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get totalWargani => 'Total Wargani';

  @override
  String get totalDonations => 'Total Donations';

  @override
  String get totalExpenses => 'Total Expenses';

  @override
  String get warganiReceipt => 'Wargani Receipt';

  @override
  String get receiptNo => 'Receipt No.';

  @override
  String get date => 'Date';

  @override
  String get name => 'Name';

  @override
  String get amount => 'Amount';

  @override
  String get amountInWords => 'Amount in Words';

  @override
  String get generatePdf => 'Generate PDF';

  @override
  String get share => 'Share';

  @override
  String get download => 'Download';

  @override
  String get thankYouNote => 'Thank you for your contribution.';

  @override
  String get expenses => 'Expenses';

  @override
  String get title => 'Title';

  @override
  String get description => 'Description';

  @override
  String get addExpense => 'Add Expense';

  @override
  String get donations => 'Donations';

  @override
  String get donorName => 'Donor Name';

  @override
  String get reason => 'Reason (Optional)';

  @override
  String get addDonation => 'Add Donation';

  @override
  String get prefix => 'Prefix (e.g., Mr./Mrs.)';

  @override
  String get warganiSummary => 'Wargani Summary';

  @override
  String get donationSummary => 'Donation Summary';

  @override
  String get expensesSummary => 'Expenses Summary';

  @override
  String get totalPeople => 'Total People';

  @override
  String get totalAmount => 'Total Amount';

  @override
  String get login => 'Login';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get pleaseEnterEmail => 'Please enter your email';

  @override
  String get pleaseEnterPassword => 'Please enter your password';

  @override
  String get noUserFound => 'No user found for that email.';

  @override
  String get wrongPassword => 'Wrong password provided for that user.';

  @override
  String loginFailed(Object errorMessage) {
    return 'Login failed: $errorMessage';
  }

  @override
  String get appName => 'Wargani';

  @override
  String get dontHaveAccount => 'Don\'t have an account? Sign up';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get forgotPasswordMessage =>
      'Forgot password functionality is not yet implemented.';

  @override
  String get signUp => 'Sign Up';

  @override
  String get signUpSuccess => 'Account created successfully!';

  @override
  String get weakPassword => 'The password provided is too weak.';

  @override
  String get emailAlreadyInUse => 'The account already exists for that email.';

  @override
  String signUpFailed(Object errorMessage) {
    return 'Sign up failed: $errorMessage';
  }

  @override
  String get createAccount => 'Create New Account';

  @override
  String get pleaseEnterName => 'Please enter a name';

  @override
  String get alreadyHaveAccount => 'Already have an account? Login';

  @override
  String get cancel => 'Cancel';

  @override
  String get sendResetLink => 'Send Reset Link';

  @override
  String get passwordResetEmailSent =>
      'Password reset email sent. Check your inbox.';

  @override
  String passwordResetFailed(Object errorMessage) {
    return 'Password reset failed: $errorMessage';
  }

  @override
  String get userNotFound => 'User not found';

  @override
  String get passwordResetSuccess => 'Password reset successfully';

  @override
  String get newPassword => 'New Password';

  @override
  String get pleaseEnterNewPassword => 'Please enter new password';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get findUser => 'Find User';

  @override
  String get pleaseEnterMandalName => 'Please enter Mandal Name';

  @override
  String get pleaseEnterAddress => 'Please enter Address';

  @override
  String get pleaseEnterCurrentYear => 'Please enter Current Year';

  @override
  String get profileSaved => 'Profile Saved';

  @override
  String get userName => 'User Name';

  @override
  String get userEmail => 'User Email';

  @override
  String get pleaseEnterReceiptNo => 'Please enter a receipt number';

  @override
  String get pleaseEnterPrefix => 'Please enter a prefix';

  @override
  String get pleaseEnterAmount => 'Please enter an amount';

  @override
  String get pleaseEnterValidAmount => 'Please enter a valid amount';

  @override
  String get pleaseEnterValidNumber => 'Please enter a valid number';

  @override
  String get pleaseEnterRegistrationNo => 'Please enter a registration number';

  @override
  String get pleaseEnterAmountInWords => 'Please enter amount in words';

  @override
  String get pdfGenerated => 'PDF Generated';

  @override
  String get pdfGeneratedSuccessfully => 'PDF has been generated successfully.';

  @override
  String get ok => 'OK';

  @override
  String get saveReceipt => 'Save Receipt';

  @override
  String get clearForm => 'Clear Form';

  @override
  String get preview => 'Preview';

  @override
  String get mobileNo => 'Mobile No.';

  @override
  String get generatedBy => 'Generated By';

  @override
  String get pleaseEnterDonorName => 'Please enter a donor name';

  @override
  String get noDonationsYet => 'No donations yet.';

  @override
  String get pleaseEnterTitle => 'Please enter a title';

  @override
  String get noExpensesYet => 'No expenses yet.';

  @override
  String get downloadAllExpenses => 'Download All Expenses';

  @override
  String get shareAllExpenses => 'Share All Expenses';

  @override
  String get netBalance => 'Net Balance';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get shreeGaneshPrasanna => '|| श्री गणेश प्रसन्न ||';

  @override
  String get registrationNo => 'Registration No.';

  @override
  String ganeshotsavYear(Object year) {
    return 'Ganeshotsav $year';
  }

  @override
  String get from => 'From';

  @override
  String get cashReceived => 'Cash Received...!';

  @override
  String get thankYou => 'Thank You ...!';

  @override
  String get signature => 'Signature';

  @override
  String get logout => 'Logout';

  @override
  String get secretQuestion => 'Secret Question';

  @override
  String get pleaseEnterSecretQuestion => 'Please enter a secret question';

  @override
  String get secretAnswer => 'Secret Answer';

  @override
  String get pleaseEnterSecretAnswer => 'Please enter a secret answer';

  @override
  String get paymentMethod => 'Payment Method';

  @override
  String get cash => 'Cash';

  @override
  String get upi => 'UPI';

  @override
  String get card => 'Card';

  @override
  String get qrCode => 'QR Code';

  @override
  String get selectPaymentMethod => 'Select payment method';

  @override
  String get deleteReceipt => 'Delete Receipt';

  @override
  String deleteReceiptConfirmation(Object receiptNo) {
    return 'Are you sure you want to delete receipt #$receiptNo?\n\nThe receipt will be moved to bin folder for security and can be recovered later.';
  }

  @override
  String get delete => 'Delete';

  @override
  String receiptMovedToBin(Object receiptNo) {
    return 'Receipt #$receiptNo moved to bin folder successfully!';
  }

  @override
  String failedToDeleteReceipt(Object error) {
    return 'Failed to delete receipt: $error';
  }

  @override
  String get upiQrCode => 'UPI QR Code';

  @override
  String get close => 'Close';

  @override
  String get noQrCodeFound =>
      'No UPI QR code found in profile. Please upload QR code first.';

  @override
  String get donorsReport => 'Donors Report';

  @override
  String get donorsReportGenerated => 'Donors Report Generated!';

  @override
  String get donorsReportSuccess =>
      'Your donors report has been generated successfully.';

  @override
  String get chooseAction => 'Choose an action:';

  @override
  String get totalDonors => 'Total Donors';

  @override
  String get srNo => 'Sr. No.';

  @override
  String get paymentMethodShort => 'Payment Method';

  @override
  String get total => 'TOTAL';

  @override
  String get allReceipts => 'All Receipts';

  @override
  String failedToGenerateReport(Object error) {
    return 'Failed to generate donors report: $error';
  }

  @override
  String get binFolder => 'Bin Folder';

  @override
  String get deletedReceipts => 'Deleted Receipts';

  @override
  String get tapToRestore => 'Tap to restore';

  @override
  String get binEmpty => 'Bin is Empty';

  @override
  String get binEmptyDescription =>
      'Deleted receipts will appear here for recovery';

  @override
  String get restore => 'Restore';

  @override
  String get restoreReceipt => 'Restore Receipt';

  @override
  String restoreReceiptConfirmation(Object receiptNo) {
    return 'Are you sure you want to restore receipt #$receiptNo?';
  }

  @override
  String receiptRestored(Object receiptNo) {
    return 'Receipt #$receiptNo restored successfully!';
  }

  @override
  String failedToRestoreReceipt(Object error) {
    return 'Failed to restore receipt: $error';
  }

  @override
  String get permanentlyDelete => 'Permanently Delete';

  @override
  String permanentlyDeleteConfirmation(Object receiptNo) {
    return 'Are you sure you want to permanently delete receipt #$receiptNo? This action cannot be undone.';
  }

  @override
  String receiptPermanentlyDeleted(Object receiptNo) {
    return 'Receipt #$receiptNo permanently deleted!';
  }

  @override
  String get emptyBin => 'Empty Bin';

  @override
  String get emptyBinConfirmation =>
      'Are you sure you want to permanently delete all receipts in the bin? This action cannot be undone.';

  @override
  String get binEmptied => 'Bin emptied successfully!';

  @override
  String failedToEmptyBin(Object error) {
    return 'Failed to empty bin: $error';
  }
}
