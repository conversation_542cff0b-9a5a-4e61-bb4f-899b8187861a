import 'package:flutter/material.dart';
import 'package:wargani/utils/pdf_generator.dart';

/// Test screen specifically for testing Marathi conjunct character rendering
/// 
/// This screen tests the enhanced Unicode processing for complex Devanagari characters
class TestConjunctRenderingScreen extends StatefulWidget {
  const TestConjunctRenderingScreen({super.key});

  @override
  State<TestConjunctRenderingScreen> createState() => _TestConjunctRenderingScreenState();
}

class _TestConjunctRenderingScreenState extends State<TestConjunctRenderingScreen> {
  String _testResult = '';
  bool _isTesting = false;

  // Test cases for problematic Marathi conjuncts
  final List<Map<String, String>> _testCases = [
    {
      'category': 'Basic Conjuncts',
      'original': 'श्री गणेश प्रसन्न',
      'description': 'श्री (shri), प्र (pra) conjuncts'
    },
    {
      'category': 'Complex Words',
      'original': 'यांच्याकडून धन्यवाद',
      'description': 'च्या (chya), न्य (nya) conjuncts'
    },
    {
      'category': 'Festival Terms',
      'original': 'गणेशोत्सव पावती',
      'description': 'त्स (tsa) conjunct in उत्सव'
    },
    {
      'category': 'Receipt Terms',
      'original': 'रोख मिळाले अक्षरी',
      'description': 'क्ष (ksha) conjunct in अक्षरी'
    },
    {
      'category': 'Organization Names',
      'original': 'दोस्ती समाजसेवा मंडळ',
      'description': 'स्त (sta), ज्स (jsa) conjuncts'
    },
    {
      'category': 'Amount Words',
      'original': 'पाच हजार एकशे रुपये फक्त',
      'description': 'क्त (kta) conjunct in फक्त'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Conjunct Rendering'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Marathi Conjunct Character Test',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This test verifies that complex Marathi conjunct characters (जोडशब्द, वेलांटी, उकार) render correctly in PDF generation.',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Test cases
            Expanded(
              child: ListView.builder(
                itemCount: _testCases.length,
                itemBuilder: (context, index) {
                  final testCase = _testCases[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            testCase['category']!,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.deepOrange,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey.shade300),
                            ),
                            child: Text(
                              testCase['original']!,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            testCase['description']!,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Test button
            ElevatedButton(
              onPressed: _isTesting ? null : _runConjunctTest,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepOrange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isTesting
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Testing Unicode Processing...'),
                      ],
                    )
                  : const Text(
                      'Test Conjunct Processing',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),

            // Test results
            if (_testResult.isNotEmpty) ...[
              const SizedBox(height: 16),
              Card(
                color: _testResult.contains('✅') ? Colors.green.shade50 : Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Test Results:',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _testResult,
                        style: TextStyle(
                          color: _testResult.contains('✅') ? Colors.green.shade800 : Colors.red.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _runConjunctTest() async {
    setState(() {
      _isTesting = true;
      _testResult = 'Running Unicode processing tests...';
    });

    try {
      // Test the Unicode processing function
      final results = <String>[];
      
      for (final testCase in _testCases) {
        final original = testCase['original']!;
        final processed = PdfGenerator.ensureProperDevanagariText(original);
        
        // Check if processing was applied
        final wasProcessed = processed != original;
        results.add('${testCase['category']}: ${wasProcessed ? '✅ Processed' : '⚪ No changes needed'}');
      }

      setState(() {
        _testResult = '✅ Unicode processing test completed!\n\n${results.join('\n')}';
        _isTesting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Conjunct character test completed successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _testResult = '❌ Test failed: $e';
        _isTesting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
