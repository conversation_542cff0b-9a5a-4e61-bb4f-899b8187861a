import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/intl.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/expense_model.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/models/donation_model.dart';
import 'package:wargani/shared/widgets/custom_button.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:wargani/utils/pdf_generator.dart';

class BinFolderScreen extends StatefulWidget {
  const BinFolderScreen({super.key});

  @override
  State<BinFolderScreen> createState() => _BinFolderScreenState();
}

class _BinFolderScreenState extends State<BinFolderScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'All';
  final List<String> _filterOptions = ['All', 'Wargani Receipts', 'Donation Receipts', 'Expense Receipts'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(Icons.delete_rounded),
            const SizedBox(width: 8),
            Text(localizations.binFolder),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.download_rounded),
            tooltip: 'Download Deleted Report',
            onPressed: () => _downloadDeletedReport(),
          ),
          IconButton(
            icon: const Icon(Icons.share_rounded),
            tooltip: 'Share Deleted Report',
            onPressed: () => _shareDeletedReport(),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterDropdown(),
          Expanded(
            child: _buildAllDeletedItemsList(localizations),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: context.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: context.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.filter_list_rounded,
            color: context.colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Filter:',
            style: context.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedFilter,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                isDense: true,
              ),
              items: _filterOptions.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedFilter = newValue ?? 'All';
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllDeletedItemsList(AppLocalizations localizations) {
    return ValueListenableBuilder(
      valueListenable: HiveHelper.getDeletedWarganiBox().listenable(),
      builder: (context, Box<Wargani> warganiBox, _) {
        return ValueListenableBuilder(
          valueListenable: HiveHelper.getDeletedDonationsBox().listenable(),
          builder: (context, donationsBox, _) {
            return ValueListenableBuilder(
              valueListenable: HiveHelper.getDeletedExpensesBox().listenable(),
              builder: (context, expensesBox, _) {
                final deletedWargani = warganiBox.values.toList().cast<Wargani>();
                final deletedDonations = donationsBox.values.toList();
                final deletedExpenses = expensesBox.values.toList();

                // Filter based on selected filter
                List<dynamic> filteredItems = [];

                switch (_selectedFilter) {
                  case 'Wargani Receipts':
                    filteredItems = deletedWargani;
                    break;
                  case 'Donation Receipts':
                    filteredItems = deletedDonations;
                    break;
                  case 'Expense Receipts':
                    filteredItems = deletedExpenses;
                    break;
                  case 'All':
                  default:
                    filteredItems = [
                      ...deletedWargani,
                      ...deletedDonations,
                      ...deletedExpenses,
                    ];
                    break;
                }

                if (filteredItems.isEmpty) {
                  return _buildEmptyState(localizations, 'No deleted items found for selected filter');
                }

                return Column(
                  children: [
                    // Header with count
                    Container(
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              'Deleted Items (${filteredItems.length}) - $_selectedFilter',
                              style: context.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.orange.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.orange.shade300),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.security_rounded,
                                  color: Colors.orange.shade700,
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'View Only - Security Protected',
                                  style: context.textTheme.bodySmall?.copyWith(
                                    color: Colors.orange.shade700,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Items list
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                        itemCount: filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = filteredItems[index];

                          if (item is Wargani) {
                            return _buildDeletedReceiptCard(item, localizations, index);
                          } else if (item is Map && item.containsKey('donorName')) {
                            return _buildDeletedDonationCard(item, index);
                          } else if (item is Map && item.containsKey('title')) {
                            // For deleted expenses
                            return _buildDeletedExpenseCard(Map<String, dynamic>.from(item), index);
                          } else {
                            // Unknown item type
                            return Card(
                              margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                              child: ListTile(
                                leading: const Icon(Icons.help_outline_rounded),
                                title: const Text('Unknown Item'),
                                subtitle: const Text('Unknown item type'),
                                trailing: const Icon(Icons.security_rounded),
                              ),
                            );
                          }
                        },
                      ),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: context.colorScheme.surface,
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(
            icon: Icon(Icons.receipt_rounded),
            text: 'Deleted Receipts',
          ),
          Tab(
            icon: Icon(Icons.volunteer_activism_rounded),
            text: 'Deleted Donations',
          ),
        ],
        labelColor: context.colorScheme.primary,
        unselectedLabelColor: context.colorScheme.onSurface.withValues(alpha: 0.6),
        indicatorColor: context.colorScheme.primary,
        indicatorWeight: 3,
      ),
    );
  }

  Widget _buildDeletedReceiptsTab(AppLocalizations localizations) {
    return ValueListenableBuilder(
      valueListenable: HiveHelper.getDeletedWarganiBox().listenable(),
      builder: (context, Box<Wargani> box, _) {
        final deletedReceipts = box.values.toList().cast<Wargani>();

        if (deletedReceipts.isEmpty) {
          return _buildEmptyState(localizations, 'No deleted receipts');
        }

        return Column(
          children: [
            // Header with count
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      '${localizations.deletedReceipts} (${deletedReceipts.length})',
                      style: context.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Text(
                    'View Only - No Restore',
                    style: context.textTheme.bodySmall?.copyWith(
                      color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
            // Deleted receipts list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                itemCount: deletedReceipts.length,
                itemBuilder: (context, index) {
                  final receipt = deletedReceipts[index];
                  return _buildDeletedReceiptCard(receipt, localizations, index);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDeletedDonationsTab(AppLocalizations localizations) {
    return ValueListenableBuilder(
      valueListenable: HiveHelper.getDeletedDonationsBox().listenable(),
      builder: (context, box, _) {
        final deletedDonations = box.values.toList();

        if (deletedDonations.isEmpty) {
          return _buildEmptyState(localizations, 'No deleted donations');
        }

        return Column(
          children: [
            // Header with count
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Deleted Donations (${deletedDonations.length})',
                      style: context.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Text(
                    'View Only - Security Protected',
                    style: context.textTheme.bodySmall?.copyWith(
                      color: Colors.red.shade600,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            // Deleted donations list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                itemCount: deletedDonations.length,
                itemBuilder: (context, index) {
                  final donation = deletedDonations[index];
                  return _buildDeletedDonationCard(donation, index);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations, [String? message]) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.delete_outline_rounded,
            size: 64,
            color: context.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            localizations.binEmpty,
            style: context.textTheme.titleLarge?.copyWith(
              color: context.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            localizations.binEmptyDescription,
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDeletedDonationCard(Map<dynamic, dynamic> donation, int index) {
    final donorName = donation['donorName'] ?? 'Unknown';
    final amount = donation['amount'] ?? 0.0;
    final date = donation['date'] ?? DateTime.now();
    final deleteReason = donation['deleteReason'] ?? 'No reason provided';
    final deletedAt = donation['deletedAt'] ?? DateTime.now();

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '#${donation['originalKey'] ?? index + 1}',
                    style: context.textTheme.labelLarge?.copyWith(
                      color: Colors.red.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  '${(date as DateTime).day}/${(date as DateTime).month}/${(date as DateTime).year}',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.person_rounded,
                  size: 20,
                  color: Colors.red.shade700,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    donorName,
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  '₹${amount.toStringAsFixed(0)}',
                  style: context.textTheme.titleLarge?.copyWith(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Delete Reason:',
                    style: context.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.red.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    deleteReason,
                    style: context.textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Deleted on: ${(deletedAt as DateTime).day}/${(deletedAt as DateTime).month}/${(deletedAt as DateTime).year}',
                    style: context.textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade300),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.security_rounded,
                    color: Colors.orange.shade700,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Security Protected - View Only Mode',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: Colors.orange.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeletedReceiptCard(Wargani receipt, AppLocalizations localizations, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${localizations.receiptNo} ${receipt.receiptNo}',
                    style: context.textTheme.labelSmall?.copyWith(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  DateFormat('dd/MM/yyyy').format(receipt.date),
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '${receipt.prefix} ${receipt.name}',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            // Delete reason
            if (receipt.deleteReason != null && receipt.deleteReason!.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Colors.red.shade700,
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        'Reason: ${receipt.deleteReason}',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: Colors.red.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Amount',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: context.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                      Text(
                        '₹${receipt.amount.toStringAsFixed(2)}',
                        style: context.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
                if (receipt.mobileNo != null && receipt.mobileNo!.isNotEmpty)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Mobile',
                          style: context.textTheme.bodySmall?.copyWith(
                            color: context.colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                        Text(
                          receipt.mobileNo!,
                          style: context.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                if (receipt.paymentMethod != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: context.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      receipt.paymentMethod!,
                      style: context.textTheme.labelSmall,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            // View only - no restore option as per requirement
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'Deleted Receipt - View Only',
                style: context.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadDeletedReport() async {
    try {
      // Show report type selection dialog
      final reportType = await _showReportTypeDialog();
      if (reportType == null) return;

      // Show language selection dialog
      final selectedLanguage = await _showLanguageSelectionDialog();
      if (selectedLanguage == null) return;

      // Generate report based on selection
      await _generateReport(reportType, selectedLanguage, false);
    } catch (e) {
      context.showErrorSnackBar('Failed to download report: ${e.toString()}');
    }
  }

  Future<void> _shareDeletedReport() async {
    try {
      // Show report type selection dialog
      final reportType = await _showReportTypeDialog();
      if (reportType == null) return;

      // Show language selection dialog
      final selectedLanguage = await _showLanguageSelectionDialog();
      if (selectedLanguage == null) return;

      // Generate and share report
      await _generateReport(reportType, selectedLanguage, true);
    } catch (e) {
      context.showErrorSnackBar('Failed to share report: ${e.toString()}');
    }
  }

  Future<void> _restoreDonation(Map<dynamic, dynamic> donation, int index) async {
    try {
      // Restore to main donations box
      final donationsBox = HiveHelper.getDonationsBox();

      // Create new donation entry
      final restoredDonation = Donation(
        donorName: donation['donorName'] ?? '',
        amount: (donation['amount'] ?? 0.0).toDouble(),
        date: donation['date'] ?? DateTime.now(),
        reason: donation['reason'],
      );

      await donationsBox.add(restoredDonation);

      // Remove from deleted donations
      await HiveHelper.getDeletedDonationsBox().deleteAt(index);

      if (mounted) {
        context.showSuccessSnackBar('Donation restored successfully!');
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to restore donation: ${e.toString()}');
      }
    }
  }

  Future<void> _deleteDonationPermanently(int index) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Permanently'),
        content: const Text('This action cannot be undone. Are you sure?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await HiveHelper.getDeletedDonationsBox().deleteAt(index);
        if (mounted) {
          context.showSuccessSnackBar('Donation deleted permanently!');
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar('Failed to delete permanently: ${e.toString()}');
        }
      }
    }
  }

  Future<String?> _showReportTypeDialog() async {
    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Report Type'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.receipt_rounded),
                title: const Text('Wargani Receipts'),
                onTap: () => Navigator.of(context).pop('wargani'),
              ),
              ListTile(
                leading: const Icon(Icons.volunteer_activism_rounded),
                title: const Text('Donation Receipts'),
                onTap: () => Navigator.of(context).pop('donations'),
              ),
              ListTile(
                leading: const Icon(Icons.money_off_rounded),
                title: const Text('Expense Receipts'),
                onTap: () => Navigator.of(context).pop('expenses'),
              ),
              ListTile(
                leading: const Icon(Icons.all_inclusive_rounded),
                title: const Text('All Deleted Items'),
                onTap: () => Navigator.of(context).pop('all'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  Future<String?> _showLanguageSelectionDialog() async {
    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Language'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Text('🇮🇳'),
                title: const Text('मराठी (Marathi)'),
                onTap: () => Navigator.of(context).pop('mr'),
              ),
              ListTile(
                leading: const Text('🇮🇳'),
                title: const Text('हिंदी (Hindi)'),
                onTap: () => Navigator.of(context).pop('hi'),
              ),
              ListTile(
                leading: const Text('🇬🇧'),
                title: const Text('English'),
                onTap: () => Navigator.of(context).pop('en'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _generateReport(String reportType, String language, bool share) async {
    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Generating report...'),
            ],
          ),
        ),
      );

      // Get data based on report type
      List<dynamic> reportData = [];
      String reportTitle = '';

      switch (reportType) {
        case 'wargani':
          reportData = HiveHelper.getDeletedWarganiBox().values.toList();
          reportTitle = 'Deleted Wargani Receipts Report';
          break;
        case 'donations':
          reportData = HiveHelper.getDeletedDonationsBox().values.toList();
          reportTitle = 'Deleted Donations Report';
          break;
        case 'expenses':
          reportData = HiveHelper.getDeletedExpensesBox().values.toList();
          reportTitle = 'Deleted Expenses Report';
          break;
        case 'all':
          reportData = [
            ...HiveHelper.getDeletedWarganiBox().values.toList(),
            ...HiveHelper.getDeletedDonationsBox().values.toList(),
          ];
          reportTitle = 'All Deleted Items Report';
          break;
      }

      if (reportData.isEmpty) {
        if (mounted) Navigator.of(context).pop();
        context.showSnackBar('No data found for selected report type');
        return;
      }

      // Generate actual PDF report based on type
      String? pdfPath;

      switch (reportType) {
        case 'wargani':
          final warganiData = reportData.whereType<Wargani>().toList();
          if (warganiData.isNotEmpty) {
            pdfPath = await PdfGenerator.generateDeletedReceiptsReport(context, warganiData, languageCode: language);
          }
          break;

        case 'donations':
          final donationsData = reportData.cast<Map<dynamic, dynamic>>();
          if (donationsData.isNotEmpty) {
            pdfPath = await PdfGenerator.generateDeletedDonationsReport(context, donationsData, languageCode: language);
          }
          break;

        case 'expenses':
          final expensesData = reportData;
          pdfPath = await PdfGenerator.generateDeletedExpensesReport(context, expensesData, languageCode: language);
          break;

        case 'all':
          // Generate combined report with all types
          final warganiData = HiveHelper.getDeletedWarganiBox().values.toList();
          final donationsData = HiveHelper.getDeletedDonationsBox().values.toList();
          final expensesData = HiveHelper.getDeletedExpensesBox().values.toList();

          pdfPath = await PdfGenerator.generateCombinedDeletedReport(
            context,
            warganiData: warganiData,
            donationsData: donationsData,
            expensesData: expensesData,
            languageCode: language,
          );
          break;
      }

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show success message
      String message = '$reportTitle generated in ${_getLanguageName(language)}';

      if (pdfPath != null) {
        if (share) {
          message += ' and shared successfully!';
        } else {
          message += ' and downloaded successfully!';
        }
        if (mounted) {
          context.showSuccessSnackBar(message);
        }
      } else {
        if (mounted) {
          context.showSnackBar('Report generated but PDF creation failed');
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();
        context.showErrorSnackBar('Failed to generate report: ${e.toString()}');
      }
    }
  }

  Widget _buildDeletedExpenseCard(Map<String, dynamic> expense, int index) {
    final title = expense['title'] ?? 'Unknown Expense';
    final amount = expense['amount'] ?? 0.0;
    final description = expense['description'] ?? '';
    final deleteReason = expense['deleteReason'] ?? 'No reason provided';
    final deletedAt = expense['deletedAt'] != null
        ? DateTime.tryParse(expense['deletedAt']) ?? DateTime.now()
        : DateTime.now();
    final originalDate = expense['date'] != null
        ? DateTime.tryParse(expense['date']) ?? DateTime.now()
        : DateTime.now();

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.orange.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.receipt_long_rounded,
                    color: Colors.orange,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: context.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '₹${amount.toStringAsFixed(2)}',
                        style: context.textTheme.titleSmall?.copyWith(
                          color: Colors.orange,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                Text(
                  '${originalDate.day}/${originalDate.month}/${originalDate.year}',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
            if (description.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Description: $description',
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
            ],
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: Colors.red.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.delete_rounded,
                    color: Colors.red,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Deleted: $deleteReason',
                          style: context.textTheme.bodySmall?.copyWith(
                            color: Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          'On: ${deletedAt.day}/${deletedAt.month}/${deletedAt.year} at ${deletedAt.hour}:${deletedAt.minute.toString().padLeft(2, '0')}',
                          style: context.textTheme.bodySmall?.copyWith(
                            color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            // Security: No restore/delete options for expenses
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.security_rounded,
                    color: Colors.grey,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Secured: This item is permanently archived for security purposes.',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  String _getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'mr':
        return 'Marathi';
      case 'hi':
        return 'Hindi';
      case 'en':
        return 'English';
      default:
        return 'English';
    }
  }
}
