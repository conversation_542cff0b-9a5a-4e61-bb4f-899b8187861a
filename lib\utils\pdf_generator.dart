import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:flutter/material.dart';
import 'package:wargani/models/donation_model.dart';
import 'package:wargani/models/expense_model.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/l10n/app_localizations_en.dart';
import 'package:wargani/l10n/app_localizations_mr.dart';
import 'package:wargani/l10n/app_localizations_hi.dart';

class PdfGenerator {
  // Helper function to ensure proper Marathi/Hindi text encoding and formatting
  static String ensureProperDevanagariText(String text) {
    if (text.isEmpty) return text;

    print('🔤 Processing text: "$text"');

    // CRITICAL: Use explicit Unicode codepoints for problematic conjuncts
    // This is the most reliable way to ensure proper rendering in PDF
    String processedText = text;

    // Step 1: Replace problematic conjuncts with explicit Unicode sequences
    final Map<String, String> unicodeFixes = {
      // Most critical conjuncts with explicit Unicode
      'श्री': '\u0936\u094D\u0930\u0940',  // श + ् + र + ी
      'प्रसन्न': '\u092A\u094D\u0930\u0938\u0928\u094D\u0928', // प + ् + र + स + न + ् + न
      'प्रसन': '\u092A\u094D\u0930\u0938\u0928', // प + ् + र + स + न
      'गणेश': '\u0917\u0923\u0947\u0936', // गणेश
      'गणेशोत्सव': '\u0917\u0923\u0947\u0936\u094B\u0924\u094D\u0938\u0935', // गणेशोत्सव
      'पावती': '\u092A\u093E\u0935\u0924\u0940', // पावती
      'रसीद': '\u0930\u0938\u0940\u0926', // रसीद
      'Date': '\u0926\u093F\u0928\u093E\u0902\u0915', // Date
      'सो.श्री': '\u0938\u094B.\u0936\u094D\u0930\u0940', // सो.श्री
      'यांच्याकडून': '\u092F\u093E\u0902\u091A\u094D\u092F\u093E\u0915\u0921\u0942\u0928', // यांच्याकडून
      'रोख': '\u0930\u094B\u0916', // रोख
      'मिळाले': '\u092E\u093F\u0933\u093E\u0932\u0947', // मिळाले
      'नकद': '\u0928\u0915\u0926', // नकद
      'प्राप्त': '\u092A\u094D\u0930\u093E\u092A\u094D\u0924', // प्राप्त
      'अक्षरी': '\u0905\u0915\u094D\u0937\u0930\u0940', // अक्षरी
      'शब्दों': '\u0936\u092C\u094D\u0926\u094B\u0902', // शब्दों
      'धन्यवाद': '\u0927\u0928\u094D\u092F\u0935\u093E\u0926', // धन्यवाद
      'सही': '\u0938\u0939\u0940', // सही
      'हस्ताक्षर': '\u0939\u0938\u094D\u0924\u093E\u0915\u094D\u0937\u0930', // हस्ताक्षर

      // Common conjuncts
      'क्ष': '\u0915\u094D\u0937',        // क + ् + ष
      'त्र': '\u0924\u094D\u0930',        // त + ् + र
      'ज्ञ': '\u091C\u094D\u091E',        // ज + ् + ञ
      'प्र': '\u092A\u094D\u0930',        // प + ् + र
      'स्व': '\u0938\u094D\u0935',        // स + ् + व
      'स्थ': '\u0938\u094D\u0925',        // स + ् + थ
      'द्व': '\u0926\u094D\u0935',        // द + ् + व
      'न्त': '\u0928\u094D\u0924',        // न + ् + त
      'न्द': '\u0928\u094D\u0926',        // न + ् + द
      'स्त': '\u0938\u094D\u0924',        // स + ् + त
      'स्प': '\u0938\u094D\u092A',        // स + ् + प
      'स्न': '\u0938\u094D\u0928',        // स + ् + न
      'स्म': '\u0938\u094D\u092E',        // स + ् + म
      'न्य': '\u0928\u094D\u092F',        // न + ् + य
      'त्य': '\u0924\u094D\u092F',        // त + ् + य
      'द्य': '\u0926\u094D\u092F',        // द + ् + य
      'म्प': '\u092E\u094D\u092A',        // म + ् + प
      'म्ब': '\u092E\u094D\u092C',        // म + ् + ब
      'न्ध': '\u0928\u094D\u0927',        // न + ् + ध
      'च्च': '\u091A\u094D\u091A',        // च + ् + च
      'त्त': '\u0924\u094D\u0924',        // त + ् + त
      'द्द': '\u0926\u094D\u0926',        // द + ् + द
      'ल्ल': '\u0932\u094D\u0932',        // ल + ् + ल
      'न्न': '\u0928\u094D\u0928',        // न + ् + न
      'च्या': '\u091A\u094D\u092F\u093E', // च्या
      'त्सव': '\u0924\u094D\u0938\u0935', // त्सव
    };

    // Apply Unicode fixes
    unicodeFixes.forEach((broken, fixed) {
      if (processedText.contains(broken)) {
        processedText = processedText.replaceAll(broken, fixed);
        print('🔧 Unicode fix: $broken → $fixed');
      }
    });

    // Step 2: Normalize individual characters
    final Map<String, String> characterFixes = {
      'ि': '\u093F',     // i-matra
      'ी': '\u0940',     // ii-matra
      'ु': '\u0941',     // u-matra
      'ू': '\u0942',     // uu-matra
      'े': '\u0947',     // e-matra
      'ै': '\u0948',     // ai-matra
      'ो': '\u094B',     // o-matra
      'ौ': '\u094C',     // au-matra
      'ं': '\u0902',     // anusvara
      'ः': '\u0903',     // visarga
      'ँ': '\u0901',     // candrabindu
      '्': '\u094D',     // halant/virama
      '़': '\u093C',     // nukta
    };

    characterFixes.forEach((original, fixed) {
      processedText = processedText.replaceAll(original, fixed);
    });

    // Step 3: Final cleanup
    processedText = processedText
        .replaceAll(RegExp(r'\s+'), ' ') // Multiple spaces to single
        .replaceAll(RegExp(r'\u094D\s+'), '\u094D') // Remove spaces after halant
        .trim();

    print('✅ Unicode processed result: "$processedText"');
    return processedText;
  }

  // Helper function to create Devanagari text widget with proper styling and spacing
  static pw.Widget _createDevanagariText(
    String text,
    pw.Font font,
    double fontSize, {
    PdfColor? color,
    pw.FontWeight? fontWeight,
    pw.TextAlign? textAlign,
    double? letterSpacing,
    double? wordSpacing,
  }) {
    // Process text for better conjunct character rendering
    final processedText = ensureProperDevanagariText(text);

    print('📝 Creating Devanagari text widget for: "$processedText"');

    return pw.Container(
      // Add slight padding to prevent character clipping
      padding: const pw.EdgeInsets.symmetric(horizontal: 0.5, vertical: 0.5),
      child: pw.Text(
        processedText,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color ?? PdfColors.black,
          fontWeight: fontWeight,
          // Optimized spacing specifically for Devanagari conjuncts
          letterSpacing: letterSpacing ?? 0.0, // No extra letter spacing for conjuncts
          wordSpacing: wordSpacing ?? 1.0,
          // Proper line height for Devanagari text
          height: 1.5, // Increased for better conjunct rendering
        ),
        textAlign: textAlign ?? pw.TextAlign.left,
        textDirection: pw.TextDirection.ltr, // Devanagari is LTR
        // Critical: Don't break conjunct characters
        softWrap: true, // Allow wrapping but preserve conjuncts
        maxLines: null, // Allow multiple lines if needed
      ),
    );
  }

  // Helper function to get proper font for Devanagari text with conjunct support
  static Future<pw.Font> _getDevanagariFont() async {
    // Try multiple fonts in order of preference for conjunct character support
    final fontAttempts = [
      () async {
        print('🔤 Attempting Noto Sans Devanagari Regular (Primary)...');
        return await PdfGoogleFonts.notoSansDevanagariRegular();
      },
      () async {
        print('🔤 Attempting Noto Serif Devanagari Regular (Secondary)...');
        return await PdfGoogleFonts.notoSerifDevanagariRegular();
      },
      () async {
        print('🔤 Attempting Hind Regular (Google Fonts)...');
        return await PdfGoogleFonts.hindRegular();
      },
      () async {
        print('🔤 Attempting Kalam Regular (Google Fonts)...');
        return await PdfGoogleFonts.kalamRegular();
      },
      () async {
        print('🔤 Attempting Hind font from assets (Local fallback)...');
        final fontData = await rootBundle.load("assets/fonts/Hind-Regular.ttf");
        return pw.Font.ttf(fontData);
      },
    ];

    for (int i = 0; i < fontAttempts.length; i++) {
      try {
        final font = await fontAttempts[i]();
        print('✅ Successfully loaded font (attempt ${i + 1}): ${fontAttempts[i].toString()}');
        return font;
      } catch (e) {
        print('⚠️ Font attempt ${i + 1} failed: $e');
        if (i == fontAttempts.length - 1) {
          // Last attempt failed
          throw Exception('❌ CRITICAL: No Devanagari font could be loaded. Please ensure internet connectivity or add Hind-Regular.ttf to assets/fonts/. Conjunct characters will not render properly.');
        }
      }
    }

    // This should never be reached, but just in case
    throw Exception('❌ Unexpected error in font loading');
  }

  static Future<pw.Font> _getDevanagariBoldFont() async {
    // Try multiple bold fonts for best conjunct character support
    final boldFontAttempts = [
      () async {
        print('🔤 Attempting Noto Sans Devanagari Bold...');
        return await PdfGoogleFonts.notoSansDevanagariBold();
      },
      () async {
        print('🔤 Attempting Noto Serif Devanagari Bold...');
        return await PdfGoogleFonts.notoSerifDevanagariBold();
      },
      () async {
        print('🔤 Falling back to regular Noto Sans Devanagari...');
        return await PdfGoogleFonts.notoSansDevanagariRegular();
      },
      () async {
        print('🔤 Final fallback to regular Devanagari font...');
        return await _getDevanagariFont();
      },
    ];

    for (int i = 0; i < boldFontAttempts.length; i++) {
      try {
        final font = await boldFontAttempts[i]();
        print('✅ Successfully loaded bold font (attempt ${i + 1})');
        return font;
      } catch (e) {
        print('⚠️ Bold font attempt ${i + 1} failed: $e');
        if (i == boldFontAttempts.length - 1) {
          // This should not happen as the last attempt calls _getDevanagariFont
          throw Exception('❌ CRITICAL: No bold Devanagari font could be loaded');
        }
      }
    }

    // This should never be reached
    throw Exception('❌ Unexpected error in bold font loading');
  }
  static Future<String?> generateProfessionalWarganiReceipt(
      BuildContext context, Wargani wargani, String? userName, {
      String? forceLanguage, // Optional parameter to force specific language
      }) async {
    // Use forced language or current context language
    AppLocalizations localizations;
    if (forceLanguage != null) {
      // Create localizations for forced language
      switch (forceLanguage) {
        case 'mr':
          localizations = AppLocalizationsMr();
          break;
        case 'hi':
          localizations = AppLocalizationsHi();
          break;
        case 'en':
        default:
          localizations = AppLocalizationsEn();
          break;
      }
    } else {
      localizations = AppLocalizations.of(context)!;
    }
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }

    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    // Load fonts - Use proper Devanagari fonts for better Marathi/Hindi support
    final font = await _getDevanagariFont();
    final boldFont = await _getDevanagariBoldFont();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4, // Same as expense receipt - A4 portrait
        margin: const pw.EdgeInsets.all(20), // Same margin as expense receipt
        build: (pw.Context context) {
          return _buildProfessionalReceiptLayout(
            localizations,
            font,
            boldFont,
            profile,
            wargani,
            userName,
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      // Create filename with donor name and receipt number
      final sanitizedName = wargani.name.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_');
      final file = File("${output.path}/Receipt_${wargani.receiptNo}_${sanitizedName}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static Future<String?> generate(
      BuildContext context, Wargani wargani, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content:
                Text(localizations.profileSaved), // Placeholder for profile needed
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    // Load proper Devanagari font for better Marathi/Hindi support
    final font = await _getDevanagariFont();

    final shivajiMaharajImage =
        await rootBundle.load("assets/images/shivaji_maharaj.png");
    final ambedkarImage = await rootBundle.load("assets/images/ambedkar.png");

    final shivajiMaharajImageProvider =
        pw.MemoryImage(shivajiMaharajImage.buffer.asUint8List());
    final ambedkarImageProvider =
        pw.MemoryImage(ambedkarImage.buffer.asUint8List());

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4, // Change to A4 portrait like expense receipt
        margin: const pw.EdgeInsets.all(20), // Add proper margin
        build: (pw.Context context) {
          // Single receipt layout instead of side-by-side for better footer visibility
          return _buildReceiptSide(
            localizations,
            font,
            profile,
            wargani,
            shivajiMaharajImageProvider,
            ambedkarImageProvider,
            ganeshaImage: profile.leftLogoBytes != null
                ? pw.MemoryImage(profile.leftLogoBytes!)
                : null,
            userName: userName,
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/receipt_wargani_${wargani.receiptNo}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static pw.Widget _buildProfessionalReceiptLayout(
    AppLocalizations localizations,
    pw.Font font,
    pw.Font boldFont,
    Profile profile,
    Wargani wargani,
    String? userName,
  ) {
    // Get current locale to determine language
    final isMarathi = localizations.localeName == 'mr';
    final isHindi = localizations.localeName == 'hi';
    final isEnglish = localizations.localeName == 'en';

    return pw.Container(
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex("#FFF8DC"), // Cornsilk background like in image
        border: pw.Border.all(color: PdfColors.orange, width: 2),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        children: [
          // Header with logos and title - Increased padding for better spacing
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(15), // Increased from 10 to 15
            decoration: pw.BoxDecoration(
              color: PdfColor.fromHex("#FF8C00"), // Orange header
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(6),
                topRight: pw.Radius.circular(6),
              ),
            ),
            child: pw.Row(
              children: [
                // Left logo (if available) - Made bigger and more prominent
                if (profile.leftLogoBytes != null) ...[
                  pw.Container(
                    height: 80,
                    width: 80,
                    decoration: pw.BoxDecoration(
                      borderRadius: pw.BorderRadius.circular(8),
                      border: pw.Border.all(color: PdfColors.white, width: 2),
                    ),
                    child: pw.Image(
                      pw.MemoryImage(profile.leftLogoBytes!),
                      fit: pw.BoxFit.cover,
                    ),
                  ),
                  pw.SizedBox(width: 15),
                ],

                // Center content
                pw.Expanded(
                  child: pw.Column(
                    children: [
                      // Custom header text row (3 positions)
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          // Left header text
                          pw.Expanded(
                            child: profile.leftHeaderText != null && profile.leftHeaderText!.isNotEmpty
                                ? _createDevanagariText(
                                    profile.leftHeaderText!,
                                    boldFont,
                                    8, // Reduced for better fit
                                    color: PdfColors.white,
                                    textAlign: pw.TextAlign.left,
                                  )
                                : pw.Container(),
                          ),
                          // Middle header text
                          pw.Expanded(
                            flex: 2,
                            child: profile.middleHeaderText != null && profile.middleHeaderText!.isNotEmpty
                                ? _createDevanagariText(
                                    profile.middleHeaderText!,
                                    boldFont,
                                    12, // Reduced for better fit
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                    textAlign: pw.TextAlign.center,
                                    letterSpacing: 1.0,
                                  )
                                : _buildLocalizedText(
                                    englishText: '|| Shree Ganesh Prasanna ||',
                                    marathiText: '|| Shree Ganesh Prasanna ||',
                                    hindiText: '|| Shree Ganesh Prasanna ||',
                                    isMarathi: isMarathi,
                                    isHindi: isHindi,
                                    font: boldFont,
                                    fontSize: 12, // Reduced for better fit
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                    textAlign: pw.TextAlign.center,
                                    letterSpacing: 1.0,
                                  ),
                          ),
                          // Right header text
                          pw.Expanded(
                            child: profile.rightHeaderText != null && profile.rightHeaderText!.isNotEmpty
                                ? _createDevanagariText(
                                    profile.rightHeaderText!,
                                    boldFont,
                                    8, // Reduced for better fit
                                    color: PdfColors.white,
                                    textAlign: pw.TextAlign.right,
                                  )
                                : pw.Container(),
                          ),
                        ],
                      ),
                      pw.SizedBox(height: 4), // Reduced spacing for better fit

                      // Mandal name - Made bigger, bolder and more prominent (TOP CENTER)
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(vertical: 6),
                        child: _buildAdaptiveText(
                          text: profile.mandalName,
                          font: boldFont,
                          fontSize: 14, // Reduced for better fit
                          color: PdfColors.white,
                          textAlign: pw.TextAlign.center,
                          isMarathi: isMarathi,
                          isHindi: isHindi,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 5), // Reduced spacing for better fit

                      // Address - Below mandal name (CENTER)
                      _buildAdaptiveText(
                        text: profile.address,
                        font: font,
                        fontSize: 9, // Reduced for better fit
                        color: PdfColors.white,
                        textAlign: pw.TextAlign.center,
                        isMarathi: isMarathi,
                        isHindi: isHindi,
                      ),
                      pw.SizedBox(height: 8), // Reduced spacing for better fit

                      // Registration number (LEFT) and Festival name + year (RIGHT) in same row
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          // Left side - Registration number
                          if (profile.mandalRegistrationNo != null)
                            _buildLocalizedText(
                              englishText: 'Reg.No. ${profile.mandalRegistrationNo}',
                              marathiText: 'Reg.No. ${profile.mandalRegistrationNo}',
                              hindiText: 'Reg.No. ${profile.mandalRegistrationNo}',
                              isMarathi: isMarathi,
                              isHindi: isHindi,
                              font: font,
                              fontSize: 8, // Reduced for better fit
                              color: PdfColors.white,
                              letterSpacing: 0.8,
                            )
                          else
                            pw.Container(), // Empty container if no reg number

                          // Right side - Festival name and year
                          _buildLocalizedText(
                            englishText: '${profile.customFestivalName ?? 'Ganeshotsav'} ${profile.currentYear}',
                            marathiText: '${profile.customFestivalName ?? 'गणेशोत्सव'} ${profile.currentYear}',
                            hindiText: '${profile.customFestivalName ?? 'गणेशोत्सव'} ${profile.currentYear}',
                            isMarathi: isMarathi,
                            isHindi: isHindi,
                            font: boldFont,
                            fontSize: 10, // Reduced for better fit
                            color: PdfColors.white,
                            fontWeight: pw.FontWeight.bold,
                            letterSpacing: 0.8,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Right logo (if available) - Made bigger and more prominent
                if (profile.rightLogoBytes != null) ...[
                  pw.SizedBox(width: 15),
                  pw.Container(
                    height: 80,
                    width: 80,
                    decoration: pw.BoxDecoration(
                      borderRadius: pw.BorderRadius.circular(8),
                      border: pw.Border.all(color: PdfColors.white, width: 2),
                    ),
                    child: pw.Image(
                      pw.MemoryImage(profile.rightLogoBytes!),
                      fit: pw.BoxFit.cover,
                    ),
                  ),
                ] else ...[
                  pw.SizedBox(width: 95), // Placeholder space for bigger logo
                ],
              ],
            ),
          ),

          // Main content - Increased padding for better spacing
          pw.Expanded(
            child: pw.Padding(
              padding: const pw.EdgeInsets.all(25), // Increased from 20 to 25
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Receipt details - Proper bold/non-bold formatting with spacing
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      // Receipt No: Bold label + space + non-bold number
                      pw.Row(
                        children: [
                          _buildLocalizedText(
                            englishText: 'Receipt No:',
                            marathiText: 'Receipt No:',
                            hindiText: 'Receipt No:',
                            isMarathi: isMarathi,
                            isHindi: isHindi,
                            font: boldFont,
                            fontSize: 12, // Reduced for better fit
                            fontWeight: pw.FontWeight.bold,
                            letterSpacing: 0.8,
                          ),
                          pw.SizedBox(width: 8), // Space between label and value
                          pw.Text(
                            wargani.receiptNo.toString(),
                            style: pw.TextStyle(
                              font: font,
                              fontSize: 12, // Reduced for better fit
                              letterSpacing: 0.6,
                            ),
                          ),
                        ],
                      ),
                      // Date: Bold label + space + non-bold date
                      pw.Row(
                        children: [
                          _buildLocalizedText(
                            englishText: 'Date:',
                            marathiText: 'Date:',
                            hindiText: 'Date:',
                            isMarathi: isMarathi,
                            isHindi: isHindi,
                            font: boldFont,
                            fontSize: 11, // Reduced for better fit
                            fontWeight: pw.FontWeight.bold,
                            letterSpacing: 0.6,
                          ),
                          pw.SizedBox(width: 8), // Space between label and value
                          pw.Text(
                            DateFormat('dd/MM/yyyy').format(wargani.date),
                            style: pw.TextStyle(
                              font: font,
                              fontSize: 11, // Reduced for better fit
                              letterSpacing: 0.6,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 8), // Further reduced for footer visibility

                  // Name section - Proper bold/non-bold formatting with spacing
                  pw.Row(
                    children: [
                      // Prefix (Bold) + space + Name (non-bold) + space + "from"
                      pw.Text(
                        wargani.prefix,
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 11, // Reduced for better fit
                          fontWeight: pw.FontWeight.bold,
                          letterSpacing: 0.6,
                        ),
                      ),
                      pw.SizedBox(width: 8), // Space after prefix
                      pw.Expanded(
                        child: pw.Container(
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(color: PdfColors.black),
                            ),
                          ),
                          child: _buildAdaptiveText(
                            text: wargani.name,
                            font: font, // Non-bold font
                            fontSize: 11, // Reduced for better fit
                            isMarathi: isMarathi,
                            isHindi: isHindi,
                          ),
                        ),
                      ),
                      pw.SizedBox(width: 8), // Space before "from"
                      _buildLocalizedText(
                        englishText: 'from',
                        marathiText: 'यांच्याकडून',
                        hindiText: 'से',
                        isMarathi: isMarathi,
                        isHindi: isHindi,
                        font: font,
                        fontSize: 10, // Reduced for better fit
                        letterSpacing: 0.6,
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 6), // Further reduced for footer visibility

                  // Amount section - Proper bold/non-bold formatting with spacing
                  pw.Row(
                    children: [
                      // "Cash Received" (Bold) + space + amount (non-bold)
                      _buildLocalizedText(
                        englishText: 'Cash Received:',
                        marathiText: 'रोख मिळाले:',
                        hindiText: 'नकद प्राप्त:',
                        isMarathi: isMarathi,
                        isHindi: isHindi,
                        font: boldFont,
                        fontSize: 12, // Reduced for better fit
                        fontWeight: pw.FontWeight.bold,
                        letterSpacing: 0.6,
                      ),
                      pw.SizedBox(width: 8), // Space between label and amount
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(color: PdfColors.black, width: 2),
                          borderRadius: pw.BorderRadius.circular(5),
                        ),
                        child: pw.Text(
                          '₹ ${wargani.amount}',
                          style: pw.TextStyle(
                            font: font, // Non-bold font
                            fontSize: 14, // Reduced for better fit
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 8), // Further reduced for footer visibility

                  // Amount in words - Proper bold/non-bold formatting with spacing
                  if (wargani.amountInWords != null && wargani.amountInWords!.isNotEmpty) ...[
                    pw.Row(
                      children: [
                        // "In Words" (Bold) + space + amount in words (non-bold)
                        _buildLocalizedText(
                          englishText: 'In Words:',
                          marathiText: 'अक्षरी:',
                          hindiText: 'शब्दों में:',
                          isMarathi: isMarathi,
                          isHindi: isHindi,
                          font: boldFont,
                          fontSize: 10, // Reduced for better fit
                          fontWeight: pw.FontWeight.bold,
                          letterSpacing: 0.6,
                        ),
                        pw.SizedBox(width: 8), // Space between label and words
                        pw.Expanded(
                          child: pw.Container(
                            decoration: const pw.BoxDecoration(
                              border: pw.Border(
                                bottom: pw.BorderSide(color: PdfColors.black),
                              ),
                            ),
                            child: _buildAdaptiveText(
                              text: wargani.amountInWords!,
                              font: font, // Non-bold font
                              fontSize: 10, // Reduced for better fit
                              isMarathi: isMarathi,
                              isHindi: isHindi,
                            ),
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 40), // Further increased spacing for A4
                  ],

                  // Payment method - Small text at bottom left
                  if (wargani.paymentMethod != null && wargani.paymentMethod!.isNotEmpty) ...[
                    pw.SizedBox(height: 10),
                    pw.Align(
                      alignment: pw.Alignment.bottomLeft,
                      child: pw.Text(
                        'Payment: ${wargani.paymentMethod}',
                        style: pw.TextStyle(
                          font: font,
                          fontSize: 8, // Reduced for better fit
                          color: PdfColors.grey700,
                        ),
                      ),
                    ),
                  ],

                  pw.SizedBox(height: 15), // Further reduced for footer visibility

                  // Footer - Redesigned layout as per requirements
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      // Left side - Thank You
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          _buildLocalizedText(
                            englishText: 'Thank You...',
                            marathiText: 'धन्यवाद...',
                            hindiText: 'धन्यवाद...',
                            isMarathi: isMarathi,
                            isHindi: isHindi,
                            font: boldFont,
                            fontSize: 16,
                            fontWeight: pw.FontWeight.bold,
                            letterSpacing: 0.8,
                          ),
                          pw.SizedBox(height: 8), // Reduced space for signature
                          // Signature with user name
                          pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              _buildLocalizedText(
                                englishText: 'Signature',
                                marathiText: 'सही',
                                hindiText: 'हस्ताक्षर',
                                isMarathi: isMarathi,
                                isHindi: isHindi,
                                font: font,
                                fontSize: 14,
                                letterSpacing: 0.6,
                              ),
                              pw.SizedBox(height: 4),
                              // User name below signature
                              if (userName != null && userName.isNotEmpty)
                                pw.Text(
                                  userName,
                                  style: pw.TextStyle(
                                    font: font,
                                    fontSize: 12,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                      // Right side - Cash Received
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        children: [
                          _buildLocalizedText(
                            englishText: 'Cash Received...',
                            marathiText: 'रोख मिळाले...',
                            hindiText: 'नकद प्राप्त...',
                            isMarathi: isMarathi,
                            isHindi: isHindi,
                            font: boldFont,
                            fontSize: 16,
                            fontWeight: pw.FontWeight.bold,
                            letterSpacing: 0.8,
                          ),
                        ],
                      ),
                    ],
                  ),

                  // AMSSoftX Credit - Made smaller as requested
                  pw.SizedBox(height: 8),
                  pw.Text(
                    'Developed by AMSSoftX, Web: https://amssoftx.com',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 8, // Bigger for better visibility
                      color: PdfColors.grey600,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildReceiptSide(
    AppLocalizations localizations,
    pw.Font font,
    Profile profile,
    Wargani wargani,
    pw.ImageProvider shivajiMaharajImageProvider,
    pw.ImageProvider ambedkarImageProvider, {
    bool isCounterfoil = false,
    pw.ImageProvider? ganeshaImage,
    String? userName,
  }) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex("#FFF8DC"), // Light background like expense receipt
        border: pw.Border.all(color: PdfColors.orange, width: 3),
        borderRadius: pw.BorderRadius.circular(15),
      ),
      child: pw.Column(
        children: [
          // Header section like expense receipt
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: PdfColor.fromHex("#FF8C00"), // Orange header
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(12),
                topRight: pw.Radius.circular(12),
              ),
            ),
            child: pw.Column(
              children: [
                if (ganeshaImage != null) ...[
                  pw.Container(
                    height: 60,
                    width: 60,
                    child: pw.Image(ganeshaImage),
                  ),
                  pw.SizedBox(height: 8),
                ],
                pw.Text(
                  localizations.shreeGaneshPrasanna,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 18,
                    color: PdfColors.white,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  profile.mandalName,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 16,
                    color: PdfColors.white,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.Text(
                  profile.address,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 12,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ],
            ),
          ),

          // Main content area
          pw.Expanded(
            child: pw.Padding(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Receipt details
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        '${localizations.receiptNo}: ${wargani.receiptNo}',
                        style: pw.TextStyle(font: font, fontSize: 16, fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(
                        '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(wargani.date)}',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Name section
                  pw.Row(
                    children: [
                      pw.Text(
                        '${localizations.prefix} ',
                        style: pw.TextStyle(font: font, fontSize: 16, fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Expanded(
                        child: pw.Container(
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(color: PdfColors.black),
                            ),
                          ),
                          child: pw.Text(
                            wargani.name,
                            style: pw.TextStyle(font: font, fontSize: 16),
                          ),
                        ),
                      ),
                      pw.SizedBox(width: 8),
                      pw.Text(
                        localizations.from,
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Amount section
                  pw.Row(
                    children: [
                      pw.Text(
                        '${localizations.cashReceived}: ',
                        style: pw.TextStyle(font: font, fontSize: 16, fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(color: PdfColors.orange, width: 2),
                          borderRadius: pw.BorderRadius.circular(5),
                        ),
                        child: pw.Text(
                          '₹ ${wargani.amount}',
                          style: pw.TextStyle(font: font, fontSize: 18, fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 15),

                  // Amount in words
                  if (wargani.amountInWords != null && wargani.amountInWords!.isNotEmpty) ...[
                    pw.Row(
                      children: [
                        pw.Text(
                          '${localizations.amountInWords}: ',
                          style: pw.TextStyle(font: font, fontSize: 14, fontWeight: pw.FontWeight.bold),
                        ),
                        pw.Expanded(
                          child: pw.Container(
                            decoration: const pw.BoxDecoration(
                              border: pw.Border(
                                bottom: pw.BorderSide(color: PdfColors.black),
                              ),
                            ),
                            child: pw.Text(
                              wargani.amountInWords!,
                              style: pw.TextStyle(font: font, fontSize: 14),
                            ),
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 20),
                  ],

                  pw.Spacer(),

                  // Footer section with proper layout
                  pw.Column(
                    children: [
                      // Main footer row
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                'Thank You..',
                                style: pw.TextStyle(
                                  font: font,
                                  fontSize: 16,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                              pw.SizedBox(height: 8),
                              pw.Text(
                                'Signature',
                                style: pw.TextStyle(
                                  font: font,
                                  fontSize: 14,
                                ),
                              ),
                              if (userName != null && userName.isNotEmpty) ...[
                                pw.SizedBox(height: 4),
                                pw.Text(
                                  userName,
                                  style: pw.TextStyle(
                                    font: font,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ],
                          ),
                          pw.Text(
                            'Cash Received..',
                            style: pw.TextStyle(
                              font: font,
                              fontSize: 16,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      ),

                      pw.SizedBox(height: 15),

                      // Payment and Developer info row
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            'Payment: Cash',
                            style: pw.TextStyle(
                              font: font,
                              fontSize: 14,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            'Developed by AMSSoftX, Web: https://amssoftx.com',
                            style: pw.TextStyle(
                              font: font,
                              fontSize: 10,
                              color: PdfColors.grey600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Future<String?> generateDonationReceipt(
      BuildContext context, Donation donation, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }

    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();
    final font = await _getDevanagariFont();
    final boldFont = await _getDevanagariBoldFont();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return _buildProfessionalDonationLayout(
            localizations,
            font,
            boldFont,
            profile,
            donation,
            userName,
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file = File("${output.path}/donation_receipt_${donation.key}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static pw.Widget _buildProfessionalDonationLayout(
    AppLocalizations localizations,
    pw.Font font,
    pw.Font boldFont,
    Profile profile,
    Donation donation,
    String? userName,
  ) {
    final isMarathi = localizations.localeName == 'mr';
    final isHindi = localizations.localeName == 'hi';

    return pw.Container(
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex("#F0FFF0"), // Light green background
        border: pw.Border.all(color: PdfColors.green, width: 3),
        borderRadius: pw.BorderRadius.circular(15),
      ),
      child: pw.Column(
        children: [
          // Header
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: PdfColors.green,
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(12),
                topRight: pw.Radius.circular(12),
              ),
            ),
            child: pw.Column(
              children: [
                if (profile.leftLogoBytes != null) ...[
                  pw.Container(
                    height: 60,
                    width: 60,
                    child: pw.Image(pw.MemoryImage(profile.leftLogoBytes!)),
                  ),
                  pw.SizedBox(height: 8),
                ],
                (isMarathi || isHindi) ? _createDevanagariText(
                  isMarathi ? '|| दान पावती ||' : '|| दान रसीद ||',
                  boldFont,
                  18,
                  color: PdfColors.white,
                  fontWeight: pw.FontWeight.bold,
                  textAlign: pw.TextAlign.center,
                  letterSpacing: 1.2,
                ) : pw.Text(
                  '|| Donation Receipt ||',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 18,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  profile.mandalName,
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 16,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.Text(
                  profile.address,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 12,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ],
            ),
          ),

          // Main content
          pw.Expanded(
            child: pw.Padding(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Date
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      (isMarathi || isHindi) ? _createDevanagariText(
                        isMarathi ? 'दान पावती' : 'दान रसीद',
                        boldFont,
                        16,
                        fontWeight: pw.FontWeight.bold,
                        letterSpacing: 0.8,
                      ) : pw.Text(
                        'Donation Receipt',
                        style: pw.TextStyle(font: boldFont, fontSize: 16),
                      ),
                      (isMarathi || isHindi) ? _createDevanagariText(
                        'Date: ${DateFormat('dd/MM/yyyy').format(donation.date)}',
                        font,
                        12,
                        letterSpacing: 0.6,
                      ) : pw.Text(
                        'Date: ${DateFormat('dd/MM/yyyy').format(donation.date)}',
                        style: pw.TextStyle(font: font, fontSize: 12),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Donor name
                  pw.Row(
                    children: [
                      (isMarathi || isHindi) ? _createDevanagariText(
                        'दानदाता: ',
                        font,
                        14,
                        letterSpacing: 0.6,
                      ) : pw.Text(
                        'Donor: ',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                      pw.Expanded(
                        child: pw.Container(
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(color: PdfColors.black),
                            ),
                          ),
                          child: pw.Text(
                            donation.donorName,
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Amount
                  pw.Row(
                    children: [
                      (isMarathi || isHindi) ? _createDevanagariText(
                        isMarathi ? 'दान रक्कम: ' : 'दान राशि: ',
                        font,
                        14,
                        letterSpacing: 0.6,
                      ) : pw.Text(
                        'Donation Amount: ',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(color: PdfColors.green, width: 2),
                          borderRadius: pw.BorderRadius.circular(5),
                        ),
                        child: pw.Text(
                          '₹ ${donation.amount}',
                          style: pw.TextStyle(font: boldFont, fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 15),

                  // Reason if provided
                  if (donation.reason != null && donation.reason!.isNotEmpty) ...[
                    pw.Row(
                      children: [
                        (isMarathi || isHindi) ? _createDevanagariText(
                          'कारण: ',
                          font,
                          12,
                          letterSpacing: 0.6,
                        ) : pw.Text(
                          'Purpose: ',
                          style: pw.TextStyle(font: font, fontSize: 12),
                        ),
                        pw.Expanded(
                          child: pw.Container(
                            decoration: const pw.BoxDecoration(
                              border: pw.Border(
                                bottom: pw.BorderSide(color: PdfColors.black),
                              ),
                            ),
                            child: pw.Text(
                              donation.reason!,
                              style: pw.TextStyle(font: font, fontSize: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 20),
                  ],

                  pw.Spacer(),

                  // Footer - Same layout as wargani receipt
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      // Left side - Thank You
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          (isMarathi || isHindi) ? _createDevanagariText(
                            isMarathi ? 'आपल्या दानाबद्दल धन्यवाद!' : 'आपके दान के लिए धन्यवाद!',
                            boldFont,
                            14,
                            fontWeight: pw.FontWeight.bold,
                            letterSpacing: 0.8,
                          ) : pw.Text(
                            'Thank you for your donation!',
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                          pw.SizedBox(height: 15), // Space for signature
                          // Signature with user name
                          pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              (isMarathi || isHindi) ? _createDevanagariText(
                                isMarathi ? 'सही' : 'हस्ताक्षर',
                                font,
                                12,
                                letterSpacing: 0.6,
                              ) : pw.Text(
                                'Signature',
                                style: pw.TextStyle(font: font, fontSize: 12),
                              ),
                              pw.SizedBox(height: 4),
                              // User name below signature
                              if (userName != null && userName.isNotEmpty)
                                pw.Text(
                                  userName,
                                  style: pw.TextStyle(
                                    font: font,
                                    fontSize: 10,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                      // Right side - Cash Received
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        children: [
                          (isMarathi || isHindi) ? _createDevanagariText(
                            isMarathi ? 'रोख मिळाले...' : 'नकद प्राप्त...',
                            boldFont,
                            14,
                            fontWeight: pw.FontWeight.bold,
                            letterSpacing: 0.8,
                          ) : pw.Text(
                            'Cash Received...',
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                        ],
                      ),
                    ],
                  ),

                  // AMSSoftX Credit - Made smaller as requested
                  pw.SizedBox(height: 15),
                  pw.Text(
                    'Developed by AMSSoftX, Web: https://amssoftx.com',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 6, // Small copyright
                      color: PdfColors.grey600,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Future<String?> generateDonationReceiptOld(
      BuildContext context, Donation donation, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    final shivajiMaharajImage =
        await rootBundle.load("assets/images/shivaji_maharaj.png");
    final ambedkarImage = await rootBundle.load("assets/images/ambedkar.png");

    final shivajiMaharajImageProvider =
        pw.MemoryImage(shivajiMaharajImage.buffer.asUint8List());
    final ambedkarImageProvider =
        pw.MemoryImage(ambedkarImage.buffer.asUint8List());

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a5.landscape,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    localizations.shreeGaneshPrasanna,
                    style: pw.TextStyle(font: font, fontSize: 10),
                  ),
                  pw.Row(
                    children: [
                      pw.Image(shivajiMaharajImageProvider, height: 25),
                      pw.SizedBox(width: 5),
                      pw.Image(ambedkarImageProvider, height: 25),
                    ],
                  )
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                profile.mandalName,
                style: pw.TextStyle(
                    font: font, fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                localizations.ganeshotsavYear(profile.currentYear),
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
              pw.SizedBox(height: 10),
              pw.Text(
                '${localizations.receiptNo}: ${donation.key}', // Assuming key as receipt no
                style: pw.TextStyle(font: font),
              ),
              pw.Text(
                '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(donation.date)}',
                style: pw.TextStyle(font: font),
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                '${localizations.donorName}: ${donation.donorName}',
                style: pw.TextStyle(font: font),
              ),
              pw.Text(
                '${localizations.amount}: ₹${donation.amount.toStringAsFixed(2)}',
                style: pw.TextStyle(font: font),
              ),
              if (donation.reason != null && donation.reason!.isNotEmpty)
                pw.Text(
                  '${localizations.reason}: ${donation.reason}',
                  style: pw.TextStyle(font: font),
                ),
              pw.Spacer(),
              pw.Align(
                alignment: pw.Alignment.bottomRight,
                child: pw.Text('${localizations.signature}: ....................',
                    style: pw.TextStyle(font: font)),
              ),
              pw.Center(
                child: pw.Text(
                  localizations.thankYou,
                  style: pw.TextStyle(font: font, fontStyle: pw.FontStyle.italic),
                ),
              )
            ],
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/receipt_donation_${donation.key}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static Future<String?> generateExpenseReport(
      BuildContext context, Expense expense, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }

    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();
    final font = await _getDevanagariFont();
    final boldFont = await _getDevanagariBoldFont();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return _buildProfessionalExpenseLayout(
            localizations,
            font,
            boldFont,
            profile,
            expense,
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file = File("${output.path}/expense_report_${expense.key}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static pw.Widget _buildProfessionalExpenseLayout(
    AppLocalizations localizations,
    pw.Font font,
    pw.Font boldFont,
    Profile profile,
    Expense expense,
  ) {
    final isMarathi = localizations.localeName == 'mr';
    final isHindi = localizations.localeName == 'hi';

    return pw.Container(
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex("#FFF5F5"), // Light red background
        border: pw.Border.all(color: PdfColors.red, width: 3),
        borderRadius: pw.BorderRadius.circular(15),
      ),
      child: pw.Column(
        children: [
          // Header
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: PdfColors.red,
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(12),
                topRight: pw.Radius.circular(12),
              ),
            ),
            child: pw.Column(
              children: [
                if (profile.leftLogoBytes != null) ...[
                  pw.Container(
                    height: 60,
                    width: 60,
                    child: pw.Image(pw.MemoryImage(profile.leftLogoBytes!)),
                  ),
                  pw.SizedBox(height: 8),
                ],
                (isMarathi || isHindi) ? _createDevanagariText(
                  isMarathi ? '|| खर्च अहवाल ||' : '|| व्यय रिपोर्ट ||',
                  boldFont,
                  18,
                  color: PdfColors.white,
                  fontWeight: pw.FontWeight.bold,
                  textAlign: pw.TextAlign.center,
                  letterSpacing: 1.2,
                ) : pw.Text(
                  '|| Expense Report ||',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 18,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  profile.mandalName,
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 16,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.Text(
                  profile.address,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 12,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ],
            ),
          ),

          // Main content
          pw.Expanded(
            child: pw.Padding(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Date
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      (isMarathi || isHindi) ? _createDevanagariText(
                        isMarathi ? 'खर्च अहवाल' : 'व्यय रिपोर्ट',
                        boldFont,
                        16,
                        fontWeight: pw.FontWeight.bold,
                        letterSpacing: 0.8,
                      ) : pw.Text(
                        'Expense Report',
                        style: pw.TextStyle(font: boldFont, fontSize: 16),
                      ),
                      (isMarathi || isHindi) ? _createDevanagariText(
                        'Date: ${DateFormat('dd/MM/yyyy').format(expense.date)}',
                        font,
                        12,
                        letterSpacing: 0.6,
                      ) : pw.Text(
                        'Date: ${DateFormat('dd/MM/yyyy').format(expense.date)}',
                        style: pw.TextStyle(font: font, fontSize: 12),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Expense title
                  pw.Row(
                    children: [
                      (isMarathi || isHindi) ? _createDevanagariText(
                        isMarathi ? 'खर्चाचे नाव: ' : 'व्यय का नाम: ',
                        font,
                        14,
                        letterSpacing: 0.6,
                      ) : pw.Text(
                        'Expense Title: ',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                      pw.Expanded(
                        child: pw.Container(
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(color: PdfColors.black),
                            ),
                          ),
                          child: pw.Text(
                            expense.title,
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 20),

                  // Amount
                  pw.Row(
                    children: [
                      (isMarathi || isHindi) ? _createDevanagariText(
                        isMarathi ? 'खर्च रक्कम: ' : 'व्यय राशि: ',
                        font,
                        14,
                        letterSpacing: 0.6,
                      ) : pw.Text(
                        'Expense Amount: ',
                        style: pw.TextStyle(font: font, fontSize: 14),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(color: PdfColors.red, width: 2),
                          borderRadius: pw.BorderRadius.circular(5),
                        ),
                        child: pw.Text(
                          '₹ ${expense.amount}',
                          style: pw.TextStyle(font: boldFont, fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 15),

                  // Description
                  if (expense.description.isNotEmpty) ...[
                    pw.Row(
                      children: [
                        (isMarathi || isHindi) ? _createDevanagariText(
                          isMarathi ? 'तपशील: ' : 'विवरण: ',
                          font,
                          12,
                          letterSpacing: 0.6,
                        ) : pw.Text(
                          'Description: ',
                          style: pw.TextStyle(font: font, fontSize: 12),
                        ),
                        pw.Expanded(
                          child: pw.Container(
                            decoration: const pw.BoxDecoration(
                              border: pw.Border(
                                bottom: pw.BorderSide(color: PdfColors.black),
                              ),
                            ),
                            child: pw.Text(
                              expense.description,
                              style: pw.TextStyle(font: font, fontSize: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 20),
                  ],

                  pw.Spacer(),

                  // Footer
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          (isMarathi || isHindi) ? _createDevanagariText(
                            isMarathi ? 'मंजूर केले' : 'स्वीकृत',
                            boldFont,
                            14,
                            fontWeight: pw.FontWeight.bold,
                            letterSpacing: 0.8,
                          ) : pw.Text(
                            'Approved',
                            style: pw.TextStyle(font: boldFont, fontSize: 14),
                          ),
                          pw.SizedBox(height: 10),
                          (isMarathi || isHindi) ? _createDevanagariText(
                            isMarathi ? 'सही' : 'हस्ताक्षर',
                            font,
                            12,
                            letterSpacing: 0.6,
                          ) : pw.Text(
                            'Signature',
                            style: pw.TextStyle(font: font, fontSize: 12),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Future<String?> generateExpenseReportOld(
      BuildContext context, Expense expense, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Center(
                child: pw.Text(
                  localizations.expensesSummary,
                  style: pw.TextStyle(font: font, fontSize: 20, fontWeight: pw.FontWeight.bold),
                ),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                '${localizations.title}: ${expense.title}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.amount}: ₹${expense.amount.toStringAsFixed(2)}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.description}: ${expense.description}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Text(
                '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(expense.date)}',
                style: pw.TextStyle(font: font, fontSize: 14),
              ),
              pw.Spacer(),
              pw.Align(
                alignment: pw.Alignment.bottomRight,
                child: pw.Text('${localizations.generatedBy}: ${userName ?? 'Admin'}',
                    style: pw.TextStyle(font: font, fontSize: 10)),
              ),
            ],
          );
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/expense_report_${expense.key}.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static Future<String?> generateAllExpensesReport(
      BuildContext context, List<Expense> expenses, String? userName) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: Text(localizations.profileSaved),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }
    final profile = profileBox.getAt(0)!;
    final pdf = pw.Document();

    final font = await PdfGoogleFonts.notoSansDevanagariRegular();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return [
            pw.Center(
              child: pw.Text(
                localizations.expensesSummary,
                style: pw.TextStyle(font: font, fontSize: 24, fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.SizedBox(height: 20),
            pw.Table.fromTextArray(
              headers: [
                localizations.date,
                localizations.title,
                localizations.description,
                localizations.amount,
              ],
              data: expenses.map((e) => [
                DateFormat('dd/MM/yyyy').format(e.date),
                e.title,
                e.description,
                '₹${e.amount.toStringAsFixed(2)}',
              ]).toList(),
              headerStyle: pw.TextStyle(font: font, fontWeight: pw.FontWeight.bold),
              cellStyle: pw.TextStyle(font: font),
              border: pw.TableBorder.all(color: PdfColors.black),
              headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(2),
                1: const pw.FlexColumnWidth(3),
                2: const pw.FlexColumnWidth(4),
                3: const pw.FlexColumnWidth(2),
              },
            ),
            pw.SizedBox(height: 20),
            pw.Align(
              alignment: pw.Alignment.bottomRight,
              child: pw.Text('${localizations.generatedBy}: ${userName ?? 'Admin'}',
                  style: pw.TextStyle(font: font, fontSize: 10)),
            ),
          ];
        },
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(
          onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file =
          File("${output.path}/all_expenses_report.pdf");
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  /// Helper method to build localized text with proper font selection
  static pw.Widget _buildLocalizedText({
    required String englishText,
    required String marathiText,
    required String hindiText,
    required bool isMarathi,
    required bool isHindi,
    required pw.Font font,
    required double fontSize,
    PdfColor? color,
    pw.FontWeight? fontWeight,
    pw.TextAlign? textAlign,
    double? letterSpacing,
  }) {
    final text = isMarathi ? marathiText : (isHindi ? hindiText : englishText);

    if (isMarathi || isHindi) {
      return _createDevanagariText(
        text,
        font,
        fontSize,
        color: color,
        fontWeight: fontWeight,
        textAlign: textAlign,
        letterSpacing: letterSpacing,
      );
    } else {
      return pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
        ),
        textAlign: textAlign,
      );
    }
  }

  /// Helper method to build adaptive text that uses appropriate font based on content
  static pw.Widget _buildAdaptiveText({
    required String text,
    required pw.Font font,
    required double fontSize,
    required bool isMarathi,
    required bool isHindi,
    PdfColor? color,
    pw.FontWeight? fontWeight,
    pw.TextAlign? textAlign,
    double? letterSpacing,
  }) {
    // Check if text contains Devanagari characters
    final hasDevanagari = _containsDevanagari(text);

    if ((isMarathi || isHindi) && hasDevanagari) {
      return _createDevanagariText(
        text,
        font,
        fontSize,
        color: color,
        fontWeight: fontWeight,
        textAlign: textAlign,
        letterSpacing: letterSpacing,
      );
    } else {
      return pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
        ),
        textAlign: textAlign,
      );
    }
  }

  /// Helper method to check if text contains Devanagari characters
  static bool _containsDevanagari(String text) {
    // Devanagari Unicode range: U+0900–U+097F
    final devanagariRegex = RegExp(r'[\u0900-\u097F]');
    return devanagariRegex.hasMatch(text);
  }

  // Generate comprehensive donors report that looks like a bank statement
  static Future<String?> generateDonorsReport(BuildContext context) async {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();

    if (profileBox.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(localizations.profile),
            content: const Text('Please set up profile first to generate donors report.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }

    final profile = profileBox.getAt(0)!;
    final warganiBox = HiveHelper.getWarganiBox();
    final donors = warganiBox.values.toList().cast<Wargani>();

    if (donors.isEmpty) {
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('No Data'),
            content: const Text('No donor records found to generate report.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localizations.ok),
              ),
            ],
          ),
        );
      }
      return null;
    }

    final pdf = pw.Document();
    final font = await _getDevanagariFont();
    final boldFont = await _getDevanagariBoldFont();

    // Sort donors by date
    donors.sort((a, b) => a.date.compareTo(b.date));

    // Calculate totals
    final totalAmount = donors.fold<double>(0, (sum, donor) => sum + donor.amount);
    final totalDonors = donors.length;

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        header: (context) => _buildDonorsReportHeader(localizations, font, boldFont, profile),
        footer: (context) => _buildDonorsReportFooter(localizations, font, context.pageNumber, context.pagesCount),
        build: (context) => [
          _buildDonorsReportSummary(localizations, font, boldFont, totalDonors, totalAmount),
          pw.SizedBox(height: 20),
          _buildDonorsReportTable(localizations, font, boldFont, donors),
        ],
      ),
    );

    if (kIsWeb) {
      await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
      return null;
    } else {
      final output = await getTemporaryDirectory();
      final file = File('${output.path}/donors_report_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(await pdf.save());
      return file.path;
    }
  }

  static pw.Widget _buildDonorsReportHeader(
    AppLocalizations localizations,
    pw.Font font,
    pw.Font boldFont,
    Profile profile,
  ) {
    final isMarathi = localizations.localeName == 'mr';
    final isHindi = localizations.localeName == 'hi';

    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(bottom: pw.BorderSide(color: PdfColors.grey300, width: 1)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  _buildAdaptiveText(
                    text: profile.mandalName,
                    font: boldFont,
                    fontSize: 20,
                    isMarathi: isMarathi,
                    isHindi: isHindi,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  pw.SizedBox(height: 4),
                  _buildAdaptiveText(
                    text: profile.address,
                    font: font,
                    fontSize: 12,
                    isMarathi: isMarathi,
                    isHindi: isHindi,
                  ),
                  if (profile.mandalRegistrationNo != null) ...[
                    pw.SizedBox(height: 2),
                    _buildLocalizedText(
                      englishText: 'Reg.No. ${profile.mandalRegistrationNo}',
                      marathiText: 'Reg.No. ${profile.mandalRegistrationNo}',
                      hindiText: 'Reg.No. ${profile.mandalRegistrationNo}',
                      isMarathi: isMarathi,
                      isHindi: isHindi,
                      font: font,
                      fontSize: 10,
                    ),
                  ],
                ],
              ),
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  _buildLocalizedText(
                    englishText: 'DONORS REPORT',
                    marathiText: 'देणगीदारांचा अहवाल',
                    hindiText: 'दाताओं की रिपोर्ट',
                    isMarathi: isMarathi,
                    isHindi: isHindi,
                    font: boldFont,
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(
                    'Generated on: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
                    style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.grey600),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildDonorsReportFooter(
    AppLocalizations localizations,
    pw.Font font,
    int pageNumber,
    int totalPages,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(top: pw.BorderSide(color: PdfColors.grey300, width: 1)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'Developed by AMSSoftX | https://amssoftx.com',
            style: pw.TextStyle(font: font, fontSize: 8, color: PdfColors.grey600),
          ),
          pw.Text(
            'Page $pageNumber of $totalPages',
            style: pw.TextStyle(font: font, fontSize: 8, color: PdfColors.grey600),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildDonorsReportSummary(
    AppLocalizations localizations,
    pw.Font font,
    pw.Font boldFont,
    int totalDonors,
    double totalAmount,
  ) {
    final isMarathi = localizations.localeName == 'mr';
    final isHindi = localizations.localeName == 'hi';

    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
        children: [
          pw.Column(
            children: [
              _buildLocalizedText(
                englishText: 'Total Donors',
                marathiText: 'Total Donors',
                hindiText: 'Total Donors',
                isMarathi: isMarathi,
                isHindi: isHindi,
                font: font,
                fontSize: 12,
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                totalDonors.toString(),
                style: pw.TextStyle(font: boldFont, fontSize: 18, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.Container(width: 1, height: 40, color: PdfColors.grey400),
          pw.Column(
            children: [
              _buildLocalizedText(
                englishText: 'Total Amount',
                marathiText: 'Total Amount',
                hindiText: 'Total Amount',
                isMarathi: isMarathi,
                isHindi: isHindi,
                font: font,
                fontSize: 12,
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                '₹ ${totalAmount.toStringAsFixed(2)}',
                style: pw.TextStyle(font: boldFont, fontSize: 18, fontWeight: pw.FontWeight.bold, color: PdfColors.green),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildDonorsReportTable(
    AppLocalizations localizations,
    pw.Font font,
    pw.Font boldFont,
    List<Wargani> donors,
  ) {
    final isMarathi = localizations.localeName == 'mr';
    final isHindi = localizations.localeName == 'hi';

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400, width: 0.5),
      columnWidths: {
        0: const pw.FixedColumnWidth(60),  // Sr. No
        1: const pw.FixedColumnWidth(80),  // Receipt No
        2: const pw.FixedColumnWidth(80),  // Date
        3: const pw.FlexColumnWidth(2),    // Name
        4: const pw.FixedColumnWidth(80),  // Amount
        5: const pw.FlexColumnWidth(1),    // Payment Method
      },
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell(
              _buildLocalizedText(
                englishText: 'Sr.\nNo.',
                marathiText: 'Sr.\nNo.',
                hindiText: 'Sr.\nNo.',
                isMarathi: isMarathi,
                isHindi: isHindi,
                font: boldFont,
                fontSize: 10,
                fontWeight: pw.FontWeight.bold,
                textAlign: pw.TextAlign.center,
              ),
              isHeader: true,
            ),
            _buildTableCell(
              _buildLocalizedText(
                englishText: 'Receipt\nNo.',
                marathiText: 'Receipt\nNo',
                hindiText: 'Receipt\nNo',
                isMarathi: isMarathi,
                isHindi: isHindi,
                font: boldFont,
                fontSize: 10,
                fontWeight: pw.FontWeight.bold,
                textAlign: pw.TextAlign.center,
              ),
              isHeader: true,
            ),
            _buildTableCell(
              _buildLocalizedText(
                englishText: 'Date',
                marathiText: 'Date',
                hindiText: 'Date',
                isMarathi: isMarathi,
                isHindi: isHindi,
                font: boldFont,
                fontSize: 10,
                fontWeight: pw.FontWeight.bold,
                textAlign: pw.TextAlign.center,
              ),
              isHeader: true,
            ),
            _buildTableCell(
              _buildLocalizedText(
                englishText: 'Donor Name',
                marathiText: 'देणगीदाराचे नाव',
                hindiText: 'दाता का नाम',
                isMarathi: isMarathi,
                isHindi: isHindi,
                font: boldFont,
                fontSize: 10,
                fontWeight: pw.FontWeight.bold,
                textAlign: pw.TextAlign.center,
              ),
              isHeader: true,
            ),
            _buildTableCell(
              _buildLocalizedText(
                englishText: 'Amount\n(₹)',
                marathiText: 'रक्कम\n(₹)',
                hindiText: 'राशि\n(₹)',
                isMarathi: isMarathi,
                isHindi: isHindi,
                font: boldFont,
                fontSize: 10,
                fontWeight: pw.FontWeight.bold,
                textAlign: pw.TextAlign.center,
              ),
              isHeader: true,
            ),
            _buildTableCell(
              _buildLocalizedText(
                englishText: 'Payment\nMethod',
                marathiText: 'पेमेंट\nपद्धत',
                hindiText: 'भुगतान\nविधि',
                isMarathi: isMarathi,
                isHindi: isHindi,
                font: boldFont,
                fontSize: 10,
                fontWeight: pw.FontWeight.bold,
                textAlign: pw.TextAlign.center,
              ),
              isHeader: true,
            ),
          ],
        ),
        // Data rows
        ...donors.asMap().entries.map((entry) {
          final index = entry.key;
          final donor = entry.value;
          final isEvenRow = index % 2 == 0;

          return pw.TableRow(
            decoration: pw.BoxDecoration(
              color: isEvenRow ? PdfColors.white : PdfColors.grey50,
            ),
            children: [
              _buildTableCell(
                pw.Text(
                  (index + 1).toString(),
                  style: pw.TextStyle(font: font, fontSize: 9),
                  textAlign: pw.TextAlign.center,
                ),
              ),
              _buildTableCell(
                pw.Text(
                  donor.receiptNo.toString(),
                  style: pw.TextStyle(font: font, fontSize: 9),
                  textAlign: pw.TextAlign.center,
                ),
              ),
              _buildTableCell(
                pw.Text(
                  DateFormat('dd/MM/yyyy').format(donor.date),
                  style: pw.TextStyle(font: font, fontSize: 9),
                  textAlign: pw.TextAlign.center,
                ),
              ),
              _buildTableCell(
                _buildAdaptiveText(
                  text: '${donor.prefix} ${donor.name}',
                  font: font,
                  fontSize: 9,
                  isMarathi: isMarathi,
                  isHindi: isHindi,
                ),
              ),
              _buildTableCell(
                pw.Text(
                  donor.amount.toStringAsFixed(2),
                  style: pw.TextStyle(font: font, fontSize: 9, fontWeight: pw.FontWeight.bold),
                  textAlign: pw.TextAlign.right,
                ),
              ),
              _buildTableCell(
                pw.Text(
                  donor.paymentMethod ?? 'Cash',
                  style: pw.TextStyle(font: font, fontSize: 8),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ],
          );
        }).toList(),
        // Total row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell(pw.Container(), isHeader: true),
            _buildTableCell(pw.Container(), isHeader: true),
            _buildTableCell(pw.Container(), isHeader: true),
            _buildTableCell(
              _buildLocalizedText(
                englishText: 'TOTAL',
                marathiText: 'TOTAL',
                hindiText: 'TOTAL',
                isMarathi: isMarathi,
                isHindi: isHindi,
                font: boldFont,
                fontSize: 10,
                fontWeight: pw.FontWeight.bold,
                textAlign: pw.TextAlign.right,
              ),
              isHeader: true,
            ),
            _buildTableCell(
              pw.Text(
                donors.fold<double>(0, (sum, donor) => sum + donor.amount).toStringAsFixed(2),
                style: pw.TextStyle(font: boldFont, fontSize: 10, fontWeight: pw.FontWeight.bold, color: PdfColors.green),
                textAlign: pw.TextAlign.right,
              ),
              isHeader: true,
            ),
            _buildTableCell(pw.Container(), isHeader: true),
          ],
        ),
      ],
    );
  }

  static pw.Widget _buildTableCell(pw.Widget child, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: child,
    );
  }

  /// Generate deleted receipts report
  static Future<String?> generateDeletedReceiptsReport(BuildContext context, List<Wargani> deletedReceipts) async {
    try {
      final localizations = AppLocalizations.of(context)!;
      final profileBox = HiveHelper.getProfileBox();

      if (profileBox.isEmpty) {
        return null;
      }

      final profile = profileBox.getAt(0)!;
      final pdf = pw.Document();
      final font = await PdfGoogleFonts.notoSansDevanagariRegular();
      final boldFont = await PdfGoogleFonts.notoSansDevanagariMedium();

      // Calculate totals
      final totalDeleted = deletedReceipts.length;
      final totalAmount = deletedReceipts.fold<double>(0, (sum, item) => sum + item.amount);

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.red100,
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        profile.mandalName,
                        style: pw.TextStyle(font: boldFont, fontSize: 20),
                        textAlign: pw.TextAlign.center,
                      ),
                      pw.SizedBox(height: 8),
                      pw.Text(
                        'Deleted Receipts Report',
                        style: pw.TextStyle(font: boldFont, fontSize: 16),
                        textAlign: pw.TextAlign.center,
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text(
                        'Generated on: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
                        style: pw.TextStyle(font: font, fontSize: 12),
                        textAlign: pw.TextAlign.center,
                      ),
                    ],
                  ),
                ),
                pw.SizedBox(height: 20),

                // Summary
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey400),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      pw.Column(
                        children: [
                          pw.Text('Total Deleted Receipts', style: pw.TextStyle(font: font, fontSize: 12)),
                          pw.Text('$totalDeleted', style: pw.TextStyle(font: boldFont, fontSize: 16)),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text('Total Amount', style: pw.TextStyle(font: font, fontSize: 12)),
                          pw.Text('₹${totalAmount.toStringAsFixed(2)}', style: pw.TextStyle(font: boldFont, fontSize: 16)),
                        ],
                      ),
                    ],
                  ),
                ),
                pw.SizedBox(height: 20),

                // Table
                pw.TableHelper.fromTextArray(
                  headers: ['Receipt No', 'Date', 'Name', 'Amount', 'Payment', 'Delete Reason'],
                  data: deletedReceipts.map((receipt) => [
                    receipt.receiptNo.toString(),
                    DateFormat('dd/MM/yyyy').format(receipt.date),
                    '${receipt.prefix} ${receipt.name}',
                    '₹${receipt.amount.toStringAsFixed(2)}',
                    receipt.paymentMethod ?? 'Cash',
                    receipt.deleteReason ?? 'No reason provided',
                  ]).toList(),
                  headerStyle: pw.TextStyle(font: boldFont, fontSize: 10),
                  cellStyle: pw.TextStyle(font: font, fontSize: 9),
                  headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
                  cellHeight: 25,
                  cellAlignments: {
                    0: pw.Alignment.center,
                    1: pw.Alignment.center,
                    2: pw.Alignment.centerLeft,
                    3: pw.Alignment.centerRight,
                    4: pw.Alignment.center,
                    5: pw.Alignment.centerLeft, // Delete reason column
                  },
                ),

                pw.Spacer(),

                // Footer
                pw.Text(
                  'Note: These receipts have been deleted but are kept for record purposes.',
                  style: pw.TextStyle(font: font, fontSize: 10, fontStyle: pw.FontStyle.italic),
                ),
                pw.SizedBox(height: 10),
                pw.Text(
                  'Developed by AMSSoftX, Web: https://amssoftx.com',
                  style: pw.TextStyle(font: font, fontSize: 8, color: PdfColors.grey600),
                  textAlign: pw.TextAlign.center,
                ),
              ],
            );
          },
        ),
      );

      // For web platform, use Printing.sharePdf instead of file operations
      if (kIsWeb) {
        await Printing.sharePdf(
          bytes: await pdf.save(),
          filename: 'deleted_receipts_report_${DateTime.now().millisecondsSinceEpoch}.pdf',
        );
        return 'shared'; // Return success indicator for web
      } else {
        final output = await getTemporaryDirectory();
        final file = File('${output.path}/deleted_receipts_report_${DateTime.now().millisecondsSinceEpoch}.pdf');
        await file.writeAsBytes(await pdf.save());
        return file.path;
      }
    } catch (e) {
      print('Error generating deleted receipts report: $e');
      return null;
    }
  }
}

class NumberToWords {
  static String convert(double number) {
    // This is a simplified version. A more robust solution would be needed for a real app.
    return number.toString();
  }
}
