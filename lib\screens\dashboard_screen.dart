import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/screens/donations_screen.dart';
import 'package:wargani/screens/expenses_screen.dart';
import 'package:wargani/screens/profile_screen.dart';
import 'package:wargani/screens/wargani_receipt_screen.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/widgets/dashboard_card.dart';
import 'package:wargani/widgets/footer.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  _DashboardScreenState createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {

  void _showUpiQrCode(BuildContext context, Uint8List qrCodeBytes) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'QR Code',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Image.memory(
                    qrCodeBytes,
                    height: 250,
                    width: 250,
                    fit: BoxFit.contain,
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Scan to make payment',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final warganiBox = HiveHelper.getWarganiBox();
    final donationsBox = HiveHelper.getDonationsBox();
    final expensesBox = HiveHelper.getExpensesBox();
    final profileBox = HiveHelper.getProfileBox();

    final totalWargani = warganiBox.values.fold<double>(0, (sum, item) => sum + item.amount);
    final totalDonations = donationsBox.values.fold<double>(0, (sum, item) => sum + item.amount);
    final totalExpenses = expensesBox.values.fold<double>(0, (sum, item) => sum + item.amount);
    final totalPeople = warganiBox.length;
    final netBalance = (totalWargani + totalDonations) - totalExpenses; // Calculate net balance

    // Get profile for logos and UPI QR code
    final profile = profileBox.isNotEmpty ? profileBox.getAt(0) : null;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            // Left logo
            if (profile?.leftLogoBytes != null) ...[
              Container(
                height: 30,
                width: 30,
                margin: const EdgeInsets.only(right: 8),
                child: Image.memory(profile!.leftLogoBytes!),
              ),
            ],
            Expanded(child: Text(localizations.dashboard)),
            // Right logo
            if (profile?.rightLogoBytes != null) ...[
              Container(
                height: 30,
                width: 30,
                margin: const EdgeInsets.only(left: 8),
                child: Image.memory(profile!.rightLogoBytes!),
              ),
            ],
          ],
        ),
        actions: [
          // QR Code button
          if (profile?.qrCodeBytes != null)
            IconButton(
              icon: const Icon(Icons.qr_code),
              tooltip: 'QR Code',
              onPressed: () => _showUpiQrCode(context, profile!.qrCodeBytes!),
            ),
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ProfileScreen()),
              ).then((_) => setState(() {}));
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            // UPI QR Code Card
            if (profile?.bigImageBytes != null)
              Card(
                color: Colors.orange.shade100,
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: InkWell(
                  onTap: () {
                    // Show QR code in full screen dialog
                    showDialog(
                      context: context,
                      builder: (context) => Dialog(
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text(
                                'UPI QR Code',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Image.memory(
                                profile!.bigImageBytes!,
                                fit: BoxFit.contain,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('Close'),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                  borderRadius: BorderRadius.circular(15),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.memory(
                              profile!.bigImageBytes!,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'UPI QR Code',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
                  DashboardCard(
                    title: localizations.warganiSummary,
                    subtitle:
                        '${localizations.totalPeople}: $totalPeople\n${localizations.totalAmount}: $totalWargani',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const WarganiReceiptScreen()),
                      ).then((_) => setState(() {}));
                    },
                    icon: Icons.people,
                    color: Colors.blue.shade100,
                  ),
                  DashboardCard(
                    title: localizations.donationSummary,
                    subtitle:
                        '${localizations.totalAmount}: $totalDonations',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const DonationsScreen()),
                      ).then((_) => setState(() {}));
                    },
                    icon: Icons.volunteer_activism,
                    color: Colors.green.shade100,
                  ),
                  DashboardCard(
                    title: localizations.expensesSummary,
                    subtitle:
                        '${localizations.totalAmount}: $totalExpenses',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const ExpensesScreen()),
                      ).then((_) => setState(() {}));
                    },
                    icon: Icons.money_off,
                    color: Colors.red.shade100,
                  ),
                  DashboardCard(
                    title: localizations.netBalance, // New card for Net Balance
                    subtitle: '${localizations.totalAmount}: $netBalance',
                    onTap: () {
                      // No specific screen for net balance, maybe show a dialog or just refresh
                    },
                    icon: Icons.account_balance_wallet,
                    color: Colors.purple.shade100,
                  ),
                ],
        ),
      ),
    );
  }
}
