import 'package:flutter/material.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:share_plus/share_plus.dart';

/// Test screen for multi-language receipt generation
/// 
/// This screen allows testing receipt generation in all three supported languages:
/// - English
/// - Marathi (मराठी)
/// - Hindi (हिंदी)
class TestMultilingualReceiptScreen extends StatefulWidget {
  const TestMultilingualReceiptScreen({super.key});

  @override
  State<TestMultilingualReceiptScreen> createState() => _TestMultilingualReceiptScreenState();
}

class _TestMultilingualReceiptScreenState extends State<TestMultilingualReceiptScreen> {
  bool _isGenerating = false;
  String _status = '';

  // Sample receipt data for testing complex Marathi conjuncts
  final Wargani _sampleReceipt = Wargani(
    receiptNo: 1001,
    name: 'श्री दोस्ती समाजसेवा मंडळ दुशेरे', // Complex conjuncts for testing
    amount: 5100.0,
    date: DateTime.now(),
    prefix: 'श्री',
    mobileNo: '9876543210',
    registrationNo: 'MH/2024/001',
    amountInWords: 'पाच हजार एकशे रुपये फक्त', // Testing conjuncts in amount words
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Multi-language Receipts'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Multi-language Receipt Test',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Test receipt generation in English, Marathi, and Hindi with proper Devanagari font support.',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Sample receipt info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sample Receipt Data:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Receipt No: ${_sampleReceipt.receiptNo}'),
                    Text('Name: ${_sampleReceipt.name}'),
                    Text('Amount: ₹${_sampleReceipt.amount}'),
                    Text('Date: ${_sampleReceipt.date.toString().split(' ')[0]}'),
                    Text('Mobile: ${_sampleReceipt.mobileNo}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Conjunct testing info
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Testing Complex Marathi Conjuncts:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text('• जोडशब्द (Compound Words): श्री, प्रसन्न, गणेशोत्सव'),
                    const Text('• वेलांटी (Virama/Halant): यांच्याकडून, धन्यवाद'),
                    const Text('• उकार (Vowel Modifications): मिळाले, रोख'),
                    const Text('• Complex conjuncts: दोस्ती, समाजसेवा, दुशेरे'),
                    const SizedBox(height: 8),
                    Text(
                      'Enhanced Unicode processing ensures proper rendering!',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Colors.green.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Language test buttons
            Text(
              'Generate Receipt in Different Languages:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // English button
            _buildLanguageButton(
              language: 'en',
              label: '🇺🇸 Generate English Receipt',
              color: Colors.blue,
            ),
            const SizedBox(height: 12),

            // Marathi button
            _buildLanguageButton(
              language: 'mr',
              label: '🇮🇳 मराठी पावती तयार करा',
              color: Colors.orange,
            ),
            const SizedBox(height: 12),

            // Hindi button
            _buildLanguageButton(
              language: 'hi',
              label: '🇮🇳 हिंदी रसीद बनाएं',
              color: Colors.green,
            ),
            const SizedBox(height: 20),

            // Status display
            if (_status.isNotEmpty) ...[
              Card(
                color: _status.contains('✅') ? Colors.green.shade50 : Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    _status,
                    style: TextStyle(
                      color: _status.contains('✅') ? Colors.green.shade800 : Colors.red.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],

            const Spacer(),

            // Loading indicator
            if (_isGenerating) ...[
              const Center(
                child: CircularProgressIndicator(),
              ),
              const SizedBox(height: 16),
              const Center(
                child: Text('Generating PDF with proper fonts...'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageButton({
    required String language,
    required String label,
    required Color color,
  }) {
    return ElevatedButton(
      onPressed: _isGenerating ? null : () => _generateReceipt(language),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(
        label,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Future<void> _generateReceipt(String language) async {
    setState(() {
      _isGenerating = true;
      _status = 'Generating ${_getLanguageName(language)} receipt...';
    });

    try {
      final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
        context,
        _sampleReceipt,
        HiveHelper.getUsersBox().getAt(0)?.name ?? 'Test User',
        forceLanguage: language,
      );

      setState(() {
        _status = pdfPath != null
            ? '✅ ${_getLanguageName(language)} receipt generated successfully!\nPath: $pdfPath'
            : '✅ ${_getLanguageName(language)} receipt opened in browser (Web platform)';
        _isGenerating = false;
      });

      // Share the generated PDF
      if (pdfPath != null && mounted) {
        await Share.shareXFiles([XFile(pdfPath)]);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${_getLanguageName(language)} receipt generated and shared!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _status = '❌ Error generating ${_getLanguageName(language)} receipt: $e';
        _isGenerating = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to generate receipt: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'mr':
        return 'Marathi (मराठी)';
      case 'hi':
        return 'Hindi (हिंदी)';
      default:
        return 'Unknown';
    }
  }
}
